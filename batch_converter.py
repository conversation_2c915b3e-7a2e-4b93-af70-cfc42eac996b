import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys
import re
from datetime import datetime
import pandas as pd
from collections import OrderedDict
import copy
from item_matcher import ItemMatcher

def get_app_directory():
    """获取应用程序所在目录，兼容打包后的情况"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        return os.path.dirname(os.path.abspath(__file__))

class BatchConverter:
    def __init__(self, parent, item_data):
        self.parent = parent
        self.item_data = item_data
        self.config_file = "batch_config.json"
        self.tables = []
        self.default_config = {
            "entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE",
            "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e61",
            "entity.operatorUser": "汕尾城区护理院",
            "entity.manuf.id": "2c901acf942ba24f01942bb69a9b1e5a",
            "entity.manufLinkTellp": "110",
            "entity.manufAddress1": "测试地址"
        }
        
        # 预定义的选项配置
        self.predefined_options = {
            "entity.intoKey.id": {
                "STOCKADVANCE_IN_PURCHASE": "采购入库",
                "STOCKADVANCE_IN_RETURN": "退货入库",
                "STOCKADVANCE_IN_TRANSFER": "调拨入库"
            },
            "entity.storeroom.id": {
                "2c901acf942ba24f01942bb7166c1e61": "非城区特困长者仓库",
                "2c901acf942ba24f01942bb7166c1e62": "员工仓库",
                "2c901acf942ba24f01942bb7166c1e63": "城区特困长者仓库",
                "2c901acf942ba24f01942bb7166c1e64": "自费长者仓库"
            },
            "entity.operatorUser": {
                "汕尾城区护理院": "汕尾城区护理院",
                "管理员": "管理员",
                "操作员1": "操作员1"
            }
        }
        
        self.load_config()
        self.create_window()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.default_config.update(config.get('default_config', {}))
                    self.tables = config.get('tables', [])
                    self.predefined_options.update(config.get('predefined_options', {}))
            except Exception as e:
                messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
    
    def save_config(self):
        """保存配置文件"""
        config = {
            'default_config': self.default_config,
            'tables': self.tables,
            'predefined_options': self.predefined_options
        }
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置文件失败: {str(e)}")
    
    def create_window(self):
        """创建批量转换窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("批量转换配置")
        self.window.geometry("1200x800")
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 默认配置页面
        self.create_default_config_tab(notebook)
        
        # 表格配置页面
        self.create_tables_config_tab(notebook)
        
        # 预定义选项配置页面
        self.create_options_config_tab(notebook)
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="开始批量转换", command=self.start_batch_conversion).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
    
    def create_default_config_tab(self, notebook):
        """创建默认配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="默认配置")
        
        # 创建滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", on_mousewheel)
        
        # 配置项
        self.default_entries = {}
        
        # 入库类型
        ttk.Label(scrollable_frame, text="入库类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.default_entries['entity.intoKey.id'] = ttk.Combobox(scrollable_frame, width=40)
        self.default_entries['entity.intoKey.id']['values'] = [f"{k} ({v})" for k, v in self.predefined_options['entity.intoKey.id'].items()]
        self.default_entries['entity.intoKey.id'].grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 仓库ID
        ttk.Label(scrollable_frame, text="仓库ID:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.default_entries['entity.storeroom.id'] = ttk.Combobox(scrollable_frame, width=40)
        self.default_entries['entity.storeroom.id']['values'] = [f"{k} ({v})" for k, v in self.predefined_options['entity.storeroom.id'].items()]
        self.default_entries['entity.storeroom.id'].grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 操作员
        ttk.Label(scrollable_frame, text="操作员:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.default_entries['entity.operatorUser'] = ttk.Combobox(scrollable_frame, width=40)
        self.default_entries['entity.operatorUser']['values'] = [f"{k} ({v})" for k, v in self.predefined_options['entity.operatorUser'].items()]
        self.default_entries['entity.operatorUser'].grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 其他配置项
        other_configs = [
            ('entity.manuf.id', '制造商ID'),
            ('entity.manufLinkTellp', '制造商电话'),
            ('entity.manufAddress1', '制造商地址')
        ]
        
        for i, (key, label) in enumerate(other_configs, start=3):
            ttk.Label(scrollable_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, padx=5, pady=5)
            self.default_entries[key] = ttk.Entry(scrollable_frame, width=40)
            self.default_entries[key].grid(row=i, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 设置默认值
        for key, entry in self.default_entries.items():
            if key in self.default_config:
                if isinstance(entry, ttk.Combobox):
                    # 对于下拉框，查找匹配的值
                    value = self.default_config[key]
                    for option in entry['values']:
                        if option.startswith(value):
                            entry.set(option)
                            break
                    else:
                        entry.set(value)
                else:
                    entry.insert(0, self.default_config[key])
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_tables_config_tab(self, notebook):
        """创建表格配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="表格配置")
        
        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="添加表格", command=self.add_table).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除表格", command=self.delete_table).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="编辑表格", command=self.edit_table).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="批量粘贴表格", command=self.batch_paste_tables).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="清空表格", command=self.clear_all_tables).pack(side=tk.LEFT, padx=(0, 5))
        
        # 表格列表
        columns = ('名称', '描述', '商品数量', '状态')
        self.tables_tree = ttk.Treeview(frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tables_tree.heading(col, text=col)
            self.tables_tree.column(col, width=150)
        
        # 滚动条
        tables_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.tables_tree.yview)
        self.tables_tree.configure(yscrollcommand=tables_scrollbar.set)
        
        self.tables_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        tables_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 5), pady=5)
        
        # 刷新表格列表
        self.refresh_tables_list()
    
    def create_options_config_tab(self, notebook):
        """创建预定义选项配置页面"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="选项配置")
        
        ttk.Label(frame, text="在这里可以配置下拉选项的实际值和备注信息", font=('微软雅黑', 10)).pack(pady=10)
        
        # 创建选项配置界面
        options_notebook = ttk.Notebook(frame)
        options_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        for option_key, options in self.predefined_options.items():
            self.create_option_config_frame(options_notebook, option_key, options)
    
    def create_option_config_frame(self, parent, option_key, options):
        """创建单个选项配置框架"""
        frame = ttk.Frame(parent)
        parent.add(frame, text=option_key.split('.')[-1])
        
        # 工具栏
        toolbar = ttk.Frame(frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="添加选项", 
                  command=lambda: self.add_option(option_key)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除选项", 
                  command=lambda: self.delete_option(option_key)).pack(side=tk.LEFT)
        
        # 选项列表
        columns = ('实际值', '备注')
        tree = ttk.Treeview(frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=200)
        
        # 填充数据
        for value, comment in options.items():
            tree.insert('', 'end', values=(value, comment))
        
        tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 保存树引用
        setattr(self, f'options_tree_{option_key.replace(".", "_")}', tree)
    
    def add_option(self, option_key):
        """添加选项"""
        dialog = OptionDialog(self.window, "添加选项")
        if dialog.result:
            value, comment = dialog.result
            self.predefined_options[option_key][value] = comment
            # 刷新树
            tree = getattr(self, f'options_tree_{option_key.replace(".", "_")}')
            tree.insert('', 'end', values=(value, comment))
    
    def delete_option(self, option_key):
        """删除选项"""
        tree = getattr(self, f'options_tree_{option_key.replace(".", "_")}')
        selected = tree.selection()
        if selected:
            item = tree.item(selected[0])
            value = item['values'][0]
            del self.predefined_options[option_key][value]
            tree.delete(selected[0])
    
    def add_table(self):
        """添加表格"""
        try:
            dialog = TableConfigDialog(self.window, "添加表格", self.default_config, self.predefined_options, self.item_data)
            # 等待对话框关闭
            dialog.dialog.wait_window()
            if dialog.result:
                self.tables.append(dialog.result)
                self.refresh_tables_list()
        except Exception as e:
            messagebox.showerror("错误", f"添加表格时出现错误：{str(e)}")
    
    def delete_table(self):
        """删除表格"""
        selected = self.tables_tree.selection()
        if selected:
            index = self.tables_tree.index(selected[0])
            del self.tables[index]
            self.refresh_tables_list()
    
    def edit_table(self):
        """编辑表格"""
        selected = self.tables_tree.selection()
        if selected:
            try:
                index = self.tables_tree.index(selected[0])
                table_config = self.tables[index]
                dialog = TableConfigDialog(self.window, "编辑表格", self.default_config, self.predefined_options, self.item_data, table_config)
                # 等待对话框关闭
                dialog.dialog.wait_window()
                if dialog.result:
                    self.tables[index] = dialog.result
                    self.refresh_tables_list()
            except Exception as e:
                messagebox.showerror("错误", f"编辑表格时出现错误：{str(e)}")
    
    def refresh_tables_list(self):
        """刷新表格列表"""
        for item in self.tables_tree.get_children():
            self.tables_tree.delete(item)
        
        for table in self.tables:
            goods_count = len(table.get('goods', []))
            status = "已配置" if goods_count > 0 else "未配置商品"
            self.tables_tree.insert('', 'end', values=(
                table.get('name', ''),
                table.get('description', ''),
                goods_count,
                status
            ))
    
    def batch_paste_tables(self):
        """批量粘贴表格"""
        try:
            dialog = BatchPasteTablesDialog(self.window, self.default_config, self.predefined_options, self.item_data)
            # 等待对话框关闭
            dialog.dialog.wait_window()
            if dialog.result:
                # 添加解析出的表格到配置中
                for table_config in dialog.result:
                    self.tables.append(table_config)
                # 刷新表格列表
                self.refresh_tables_list()
                # 保存配置
                self.save_config()
                messagebox.showinfo("成功", f"成功添加 {len(dialog.result)} 个表格")
        except Exception as e:
            messagebox.showerror("错误", f"批量粘贴表格时出现错误：{str(e)}")

    def clear_all_tables(self):
        """清空所有表格"""
        if not self.tables:
            messagebox.showinfo("提示", "当前没有表格需要清空")
            return

        if messagebox.askyesno("确认", f"确定要清空所有 {len(self.tables)} 个表格吗？此操作不可撤销。"):
            self.tables.clear()
            self.refresh_tables_list()
            self.save_config()
            messagebox.showinfo("完成", "已清空所有表格")

    def start_batch_conversion(self):
        """开始批量转换"""
        if not self.tables:
            messagebox.showwarning("警告", "请先配置至少一个表格")
            return

        # 更新默认配置
        for key, entry in self.default_entries.items():
            value = entry.get()
            if isinstance(entry, ttk.Combobox) and ' (' in value:
                # 提取实际值
                value = value.split(' (')[0]
            self.default_config[key] = value

        # 保存配置
        self.save_config()

        # 创建输出目录
        output_dir = os.path.join(get_app_directory(), 'out')
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 清空输出文件夹中的所有文件
        self.clear_output_directory(output_dir)
        
        # 转换每个表格
        success_count = 0
        for i, table in enumerate(self.tables):
            try:
                json_data = self.convert_table_to_json(table)
                filename = f"{table.get('name', f'table_{i+1}')}.json"
                filepath = os.path.join(output_dir, filename)
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                
                success_count += 1
            except Exception as e:
                messagebox.showerror("错误", f"转换表格 '{table.get('name', f'table_{i+1}')}' 失败: {str(e)}")
        
        messagebox.showinfo("完成", f"批量转换完成！成功转换 {success_count}/{len(self.tables)} 个表格\n输出目录: {output_dir}")

    def clear_output_directory(self, output_dir):
        """清空输出文件夹中的所有文件"""
        try:
            import glob
            # 删除输出目录中的所有.json文件
            json_files = glob.glob(os.path.join(output_dir, "*.json"))
            deleted_count = 0
            for file_path in json_files:
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    print(f"删除文件 {file_path} 失败: {str(e)}")

            if deleted_count > 0:
                print(f"已清空输出文件夹，删除了 {deleted_count} 个文件")
        except Exception as e:
            print(f"清空输出文件夹时出错: {str(e)}")

    def convert_table_to_json(self, table_config):
        """将表格配置转换为JSON"""
        # 使用OrderedDict确保字段顺序正确
        json_data = OrderedDict()
        
        # 按照template.json的顺序添加字段
        json_data["doOpt"] = "saveadd"
        json_data["entity.id"] = ""
        json_data["exEntity.id"] = ""
        json_data["exEntity.elderId"] = ""
        json_data["exEntity.elderNo"] = ""
        json_data["fromOutHouse"] = "false"
        json_data["outHouseId"] = ""
        
        # 添加默认配置字段
        default_config = copy.deepcopy(self.default_config)
        
        # 表格特定配置
        table_config_data = table_config.get('config', {})
        
        # 入库类型：优先使用表格配置，否则使用默认配置
        json_data["entity.intoKey.id"] = table_config_data.get("entity.intoKey.id", 
                                                              default_config.get("entity.intoKey.id", "STOCKADVANCE_IN_PURCHASE"))
        
        json_data["entity.intoDate"] = table_config_data.get("entity.intoDate", "")
        
        # 仓库ID：优先使用表格配置，否则使用默认配置
        json_data["entity.storeroom.id"] = table_config_data.get("entity.storeroom.id",
                                                                default_config.get("entity.storeroom.id", ""))
        json_data["entity.totalMoney"] = "0"  # 稍后计算
        json_data["entity.deliveryNnoteNo"] = table_config_data.get("entity.deliveryNnoteNo", "")
        
        # 操作员：优先使用表格配置，否则使用默认配置
        json_data["entity.operatorUser"] = table_config_data.get("entity.operatorUser",
                                                                default_config.get("entity.operatorUser", ""))
        json_data["entity.intoNO"] = ""
        json_data["entity.actualMoney"] = "0"  # 稍后计算
        json_data["entity.notes"] = table_config_data.get("entity.notes", "")
        json_data["entity.manuf.id"] = default_config.get("entity.manuf.id", "")
        json_data["entity.manufLinkTellp"] = default_config.get("entity.manufLinkTellp", "")
        json_data["entity.manufAddress1"] = default_config.get("entity.manufAddress1", "")
        json_data["goodsList"] = ""
        
        # 添加商品列表
        goods_list = table_config.get('goods', [])
        
        # 计算总金额
        total_money = 0
        items_array = []
        
        # 先添加所有商品的平铺字段
        for i, goods in enumerate(goods_list):
            prefix = f"recList[{i}]."
            
            # 获取商品信息
            goods_name = goods.get('name', '')
            goods_id = ""
            goods_no = ""
            unit = goods.get('unit', '')
            
            if goods_name in self.item_data:
                item_info = self.item_data[goods_name]
                goods_id = item_info.get('id', '')
                goods_no = item_info.get('no', '')
                if not unit:  # 如果没有指定单位，使用物品数据中的单位
                    unit = item_info.get('unit', '')
            
            # 计算小计
            try:
                quantity = float(goods.get('quantity', 0))
                price = float(goods.get('price', 0))
                sub_money = quantity * price
                total_money += sub_money
            except:
                sub_money = 0
            
            # 按照template.json的顺序添加商品字段
            json_data[f"{prefix}goodsId"] = goods_id
            json_data[f"{prefix}goodsNo"] = goods_no
            json_data[f"{prefix}goodsCnName"] = goods_name
            json_data[f"{prefix}batchNo"] = goods.get('batchNo', '')
            json_data[f"{prefix}productionDate"] = goods.get('productionDate', '')
            json_data[f"{prefix}manufacturer"] = goods.get('manufacturer', '')
            json_data[f"{prefix}expireDate"] = goods.get('expireDate', '')
            json_data[f"{prefix}planNum"] = str(goods.get('quantity', ''))
            json_data[f"{prefix}unit"] = unit
            json_data[f"{prefix}price"] = str(goods.get('price', ''))
            json_data[f"{prefix}subMoney"] = str(sub_money)
            json_data[f"{prefix}splitUnit"] = goods.get('splitUnit', '')
            json_data[f"{prefix}splitNum"] = goods.get('splitNum', '')
            json_data[f"{prefix}notes"] = goods.get('notes', '')
            
            # items 数组中的对象
            item_obj = OrderedDict()
            item_obj[f"{prefix}goodsId"] = goods_id
            item_obj[f"{prefix}goodsNo"] = goods_no
            item_obj[f"{prefix}goodsCnName"] = goods_name
            item_obj[f"{prefix}batchNo"] = goods.get('batchNo', '')
            item_obj[f"{prefix}productionDate"] = goods.get('productionDate', '')
            item_obj[f"{prefix}manufacturer"] = goods.get('manufacturer', '')
            item_obj[f"{prefix}expireDate"] = goods.get('expireDate', '')
            item_obj[f"{prefix}planNum"] = str(goods.get('quantity', ''))
            item_obj[f"{prefix}unit"] = unit
            item_obj[f"{prefix}price"] = str(goods.get('price', ''))
            item_obj[f"{prefix}subMoney"] = str(sub_money)
            item_obj[f"{prefix}splitUnit"] = goods.get('splitUnit', '')
            item_obj[f"{prefix}splitNum"] = goods.get('splitNum', '')
            item_obj[f"{prefix}notes"] = goods.get('notes', '')
            items_array.append(item_obj)
        
        # 更新总金额
        json_data['entity.totalMoney'] = str(total_money)
        json_data['entity.actualMoney'] = str(total_money)
        
        # 添加 items 数组
        json_data['items'] = items_array
        
        return json_data
    
    def set_current_time(self, entry):
        """设置当前时间"""
        entry.delete(0, tk.END)
        entry.insert(0, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))


class OptionDialog:
    def __init__(self, parent, title):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置最小窗口大小
        self.dialog.minsize(400, 150)
        
        frame = ttk.Frame(self.dialog)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        ttk.Label(frame, text="实际值:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.value_entry = ttk.Entry(frame, width=30)
        self.value_entry.grid(row=0, column=1, pady=5, padx=(10, 0))
        
        ttk.Label(frame, text="备注:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.comment_entry = ttk.Entry(frame, width=30)
        self.comment_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
        
        # 让窗口自适应内容大小并居中
        self.dialog.update_idletasks()
        width = max(400, self.dialog.winfo_reqwidth())
        height = max(150, self.dialog.winfo_reqheight())
        
        # 获取屏幕尺寸并计算居中位置
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        self.value_entry.focus()
    
    def ok_clicked(self):
        value = self.value_entry.get().strip()
        comment = self.comment_entry.get().strip()
        
        if not value:
            messagebox.showwarning("警告", "请输入实际值")
            return
        
        self.result = (value, comment)
        self.dialog.destroy()


class TableConfigDialog:
    def __init__(self, parent, title, default_config, predefined_options, item_data, table_config=None):
        self.result = None
        self.default_config = default_config
        self.predefined_options = predefined_options
        self.item_data = item_data
        self.table_config = table_config or {}
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置最小窗口大小
        self.dialog.minsize(1000, 600)
        
        self.create_widgets()
        self.load_data()
        
        # 让窗口自适应内容大小并居中
        self.dialog.update_idletasks()
        width = max(1000, self.dialog.winfo_reqwidth())
        height = max(600, self.dialog.winfo_reqheight())
        
        # 获取屏幕尺寸并计算居中位置
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """创建控件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息
        info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text="表格名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_entry = ttk.Entry(info_frame, width=30)
        self.name_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(info_frame, text="描述:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.desc_entry = ttk.Entry(info_frame, width=30)
        self.desc_entry.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)
        
        # 配置信息
        config_frame = ttk.LabelFrame(main_frame, text="配置信息", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.config_entries = {}
        
        # 第一行：入库类型和仓库ID
        ttk.Label(config_frame, text="入库类型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.into_key_var = tk.StringVar()
        self.into_key_combo = ttk.Combobox(config_frame, textvariable=self.into_key_var, width=18, state="readonly")
        if 'entity.intoKey.id' in self.predefined_options:
            into_key_options = list(self.predefined_options['entity.intoKey.id'].values())
            self.into_key_combo['values'] = into_key_options
            # 设置默认值
            default_into_key = self.default_config.get('entity.intoKey.id', '')
            if default_into_key in self.predefined_options['entity.intoKey.id']:
                self.into_key_var.set(self.predefined_options['entity.intoKey.id'][default_into_key])
        self.into_key_combo.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(config_frame, text="仓库:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.storeroom_var = tk.StringVar()
        self.storeroom_combo = ttk.Combobox(config_frame, textvariable=self.storeroom_var, width=18, state="readonly")
        if 'entity.storeroom.id' in self.predefined_options:
            storeroom_options = list(self.predefined_options['entity.storeroom.id'].values())
            self.storeroom_combo['values'] = storeroom_options
            # 设置默认值
            default_storeroom = self.default_config.get('entity.storeroom.id', '')
            if default_storeroom in self.predefined_options['entity.storeroom.id']:
                self.storeroom_var.set(self.predefined_options['entity.storeroom.id'][default_storeroom])
        self.storeroom_combo.grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)
        
        # 第二行：操作员和入库日期
        ttk.Label(config_frame, text="操作员:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.operator_var = tk.StringVar()
        self.operator_combo = ttk.Combobox(config_frame, textvariable=self.operator_var, width=18, state="readonly")
        if 'entity.operatorUser' in self.predefined_options:
            operator_options = list(self.predefined_options['entity.operatorUser'].values())
            self.operator_combo['values'] = operator_options
            # 设置默认值
            default_operator = self.default_config.get('entity.operatorUser', '')
            if default_operator in self.predefined_options['entity.operatorUser']:
                self.operator_var.set(self.predefined_options['entity.operatorUser'][default_operator])
        self.operator_combo.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        ttk.Label(config_frame, text="入库日期:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        date_frame = ttk.Frame(config_frame)
        date_frame.grid(row=1, column=3, padx=5, pady=5, sticky=tk.W)
        
        self.date_entry = ttk.Entry(date_frame, width=20)
        self.date_entry.pack(side=tk.LEFT)
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        ttk.Button(date_frame, text="当前时间", 
                  command=lambda: self.set_current_time(self.date_entry)).pack(side=tk.LEFT, padx=(5, 0))
        
        # 第三行：送货单号
        ttk.Label(config_frame, text="送货单号:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.delivery_entry = ttk.Entry(config_frame, width=20)
        self.delivery_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 第四行：备注
        ttk.Label(config_frame, text="备注:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.notes_entry = ttk.Entry(config_frame, width=50)
        self.notes_entry.grid(row=3, column=1, columnspan=3, padx=5, pady=5, sticky=tk.W)
        
        # 商品配置
        goods_frame = ttk.LabelFrame(main_frame, text="商品配置", padding=10)
        goods_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        toolbar = ttk.Frame(goods_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="添加商品", command=self.add_goods).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除商品", command=self.delete_goods).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="粘贴商品", command=self.paste_goods).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="清空商品", command=self.clear_goods).pack(side=tk.LEFT)
        
        # 商品列表
        columns = ('商品名称', '数量', '单价', '小计', '单位', '批次号', '生产日期', '过期日期', '备注')
        self.goods_tree = ttk.Treeview(goods_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.goods_tree.heading(col, text=col)
            if col == '商品名称':
                self.goods_tree.column(col, width=150)
            elif col in ['数量', '单价', '小计']:
                self.goods_tree.column(col, width=80)
            else:
                self.goods_tree.column(col, width=100)
        
        # 滚动条
        goods_scrollbar = ttk.Scrollbar(goods_frame, orient=tk.VERTICAL, command=self.goods_tree.yview)
        self.goods_tree.configure(yscrollcommand=goods_scrollbar.set)
        
        self.goods_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        goods_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 底部按钮 - 固定在窗口底部
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 按钮居中显示
        button_container = ttk.Frame(button_frame)
        button_container.pack(anchor=tk.CENTER)
        
        ttk.Button(button_container, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_container, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
    
    def load_data(self):
        """加载数据"""
        if self.table_config:
            self.name_entry.insert(0, self.table_config.get('name', ''))
            self.desc_entry.insert(0, self.table_config.get('description', ''))
            
            config = self.table_config.get('config', {})
            
            # 加载入库类型
            if 'entity.intoKey.id' in config and 'entity.intoKey.id' in self.predefined_options:
                into_key_id = config['entity.intoKey.id']
                if into_key_id in self.predefined_options['entity.intoKey.id']:
                    self.into_key_var.set(self.predefined_options['entity.intoKey.id'][into_key_id])
            
            # 加载仓库ID
            if 'entity.storeroom.id' in config and 'entity.storeroom.id' in self.predefined_options:
                storeroom_id = config['entity.storeroom.id']
                if storeroom_id in self.predefined_options['entity.storeroom.id']:
                    self.storeroom_var.set(self.predefined_options['entity.storeroom.id'][storeroom_id])
            
            # 加载操作员
            if 'entity.operatorUser' in config and 'entity.operatorUser' in self.predefined_options:
                operator_user = config['entity.operatorUser']
                if operator_user in self.predefined_options['entity.operatorUser']:
                    self.operator_var.set(self.predefined_options['entity.operatorUser'][operator_user])
            
            # 加载其他配置
            if 'entity.intoDate' in config:
                self.date_entry.delete(0, tk.END)
                self.date_entry.insert(0, config['entity.intoDate'])
            if 'entity.deliveryNnoteNo' in config:
                self.delivery_entry.insert(0, config['entity.deliveryNnoteNo'])
            if 'entity.notes' in config:
                self.notes_entry.insert(0, config['entity.notes'])
            
            # 加载商品
            for goods in self.table_config.get('goods', []):
                self.add_goods_to_tree(goods)
    
    def add_goods(self):
        """添加商品"""
        try:
            dialog = GoodsDialog(self.dialog, "添加商品", self.item_data)
            # 等待对话框关闭
            dialog.dialog.wait_window()
            if dialog.result:
                self.add_goods_to_tree(dialog.result)
        except Exception as e:
            messagebox.showerror("错误", f"添加商品时出现错误：{str(e)}")
    
    def add_goods_to_tree(self, goods):
        """添加商品到树"""
        name = goods.get('name', '')
        quantity = goods.get('quantity', '')
        price = goods.get('price', '')
        
        # 计算小计
        try:
            sub_total = float(quantity) * float(price)
        except:
            sub_total = 0
        
        # 检查商品是否存在
        name_display = name
        if name and name not in self.item_data:
            name_display = f"❌ {name}"  # 红色标记不存在的商品
        
        self.goods_tree.insert('', 'end', values=(
            name_display,
            quantity,
            price,
            f"{sub_total:.2f}",
            goods.get('unit', ''),
            goods.get('batchNo', ''),
            goods.get('productionDate', ''),
            goods.get('expireDate', ''),
            goods.get('notes', '')
        ))
    
    def delete_goods(self):
        """删除商品"""
        selected = self.goods_tree.selection()
        if selected:
            self.goods_tree.delete(selected[0])
    
    def paste_goods(self):
        """粘贴商品"""
        try:
            dialog = PasteGoodsDialog(self.dialog, self.item_data)
            # 等待对话框关闭
            dialog.dialog.wait_window()
            if dialog.result:
                for goods in dialog.result:
                    self.add_goods_to_tree(goods)
        except Exception as e:
            messagebox.showerror("错误", f"粘贴商品时出现错误：{str(e)}")
    
    def clear_goods(self):
        """清空商品"""
        if messagebox.askyesno("确认", "确定要清空所有商品吗？"):
            for item in self.goods_tree.get_children():
                self.goods_tree.delete(item)
    
    def ok_clicked(self):
        """确定按钮点击"""
        name = self.name_entry.get().strip()
        if not name:
            messagebox.showwarning("警告", "请输入表格名称")
            return
        
        # 收集配置
        config = {}
        
        # 收集入库类型
        into_key_display = self.into_key_var.get()
        if into_key_display and 'entity.intoKey.id' in self.predefined_options:
            # 根据显示名称找到对应的ID
            for key, value in self.predefined_options['entity.intoKey.id'].items():
                if value == into_key_display:
                    config['entity.intoKey.id'] = key
                    break
        
        # 收集仓库ID
        storeroom_display = self.storeroom_var.get()
        if storeroom_display and 'entity.storeroom.id' in self.predefined_options:
            # 根据显示名称找到对应的ID
            for key, value in self.predefined_options['entity.storeroom.id'].items():
                if value == storeroom_display:
                    config['entity.storeroom.id'] = key
                    break
        
        # 收集操作员
        operator_display = self.operator_var.get()
        if operator_display and 'entity.operatorUser' in self.predefined_options:
            # 根据显示名称找到对应的值
            for key, value in self.predefined_options['entity.operatorUser'].items():
                if value == operator_display:
                    config['entity.operatorUser'] = key
                    break
        
        # 收集其他配置
        config['entity.intoDate'] = self.date_entry.get()
        config['entity.deliveryNnoteNo'] = self.delivery_entry.get()
        config['entity.notes'] = self.notes_entry.get()
        
        # 收集商品
        goods = []
        for item in self.goods_tree.get_children():
            values = self.goods_tree.item(item)['values']
            goods_name = values[0]
            # 移除错误标记
            if goods_name.startswith('❌ '):
                goods_name = goods_name[2:]
            
            goods.append({
                'name': goods_name,
                'quantity': values[1],
                'price': values[2],
                'unit': values[4],
                'batchNo': values[5],
                'productionDate': values[6],
                'expireDate': values[7],
                'notes': values[8]
            })
        
        self.result = {
            'name': name,
            'description': self.desc_entry.get().strip(),
            'config': config,
            'goods': goods
        }
        
        self.dialog.destroy()


class GoodsDialog:
    def __init__(self, parent, title, item_data, goods_data=None):
        self.result = None
        self.item_data = item_data
        self.goods_data = goods_data or {}
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置最小窗口大小
        self.dialog.minsize(500, 400)
        
        self.create_widgets()
        self.load_data()
        
        # 让窗口自适应内容大小并居中
        self.dialog.update_idletasks()
        width = max(500, self.dialog.winfo_reqwidth())
        height = max(400, self.dialog.winfo_reqheight())
        
        # 获取屏幕尺寸并计算居中位置
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置焦点
        self.name_combo.focus_set()
    
    def create_widgets(self):
        """创建控件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 商品名称
        ttk.Label(main_frame, text="商品名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        self.name_combo = ttk.Combobox(main_frame, textvariable=self.name_var, width=30)
        self.name_combo['values'] = list(self.item_data.keys())
        self.name_combo.grid(row=0, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        self.name_combo.bind('<KeyRelease>', self.on_name_change)
        self.name_combo.bind('<<ComboboxSelected>>', self.on_name_change)
        
        # 数量
        ttk.Label(main_frame, text="数量:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.quantity_entry = ttk.Entry(main_frame, width=30)
        self.quantity_entry.grid(row=1, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 单价
        ttk.Label(main_frame, text="单价:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.price_entry = ttk.Entry(main_frame, width=30)
        self.price_entry.grid(row=2, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 单位
        ttk.Label(main_frame, text="单位:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.unit_entry = ttk.Entry(main_frame, width=30)
        self.unit_entry.grid(row=3, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 批次号
        ttk.Label(main_frame, text="批次号:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.batch_entry = ttk.Entry(main_frame, width=30)
        self.batch_entry.grid(row=4, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 生产日期
        ttk.Label(main_frame, text="生产日期:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.production_entry = ttk.Entry(main_frame, width=30)
        self.production_entry.grid(row=5, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 过期日期
        ttk.Label(main_frame, text="过期日期:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.expire_entry = ttk.Entry(main_frame, width=30)
        self.expire_entry.grid(row=6, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 备注
        ttk.Label(main_frame, text="备注:").grid(row=7, column=0, sticky=tk.W, pady=5)
        self.notes_entry = ttk.Entry(main_frame, width=30)
        self.notes_entry.grid(row=7, column=1, pady=5, padx=(10, 0), sticky=tk.W)
        
        # 按钮 - 固定在窗口底部
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 按钮居中显示
        button_container = ttk.Frame(button_frame)
        button_container.pack(anchor=tk.CENTER)
        
        ttk.Button(button_container, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_container, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
    
    def on_name_change(self, event):
        """商品名称改变时自动填充单位"""
        name = self.name_var.get()
        if name in self.item_data:
            unit = self.item_data[name].get('unit', '')
            self.unit_entry.delete(0, tk.END)
            self.unit_entry.insert(0, unit)
    
    def load_data(self):
        """加载数据"""
        if self.goods_data:
            self.name_var.set(self.goods_data.get('name', ''))
            self.quantity_entry.insert(0, self.goods_data.get('quantity', ''))
            self.price_entry.insert(0, self.goods_data.get('price', ''))
            self.unit_entry.insert(0, self.goods_data.get('unit', ''))
            self.batch_entry.insert(0, self.goods_data.get('batchNo', ''))
            self.production_entry.insert(0, self.goods_data.get('productionDate', ''))
            self.expire_entry.insert(0, self.goods_data.get('expireDate', ''))
            self.notes_entry.insert(0, self.goods_data.get('notes', ''))
    
    def ok_clicked(self):
        """确定按钮点击"""
        name = self.name_var.get().strip()
        if not name:
            messagebox.showwarning("警告", "请输入商品名称")
            return
        
        self.result = {
            'name': name,
            'quantity': self.quantity_entry.get(),
            'price': self.price_entry.get(),
            'unit': self.unit_entry.get(),
            'batchNo': self.batch_entry.get(),
            'productionDate': self.production_entry.get(),
            'expireDate': self.expire_entry.get(),
            'notes': self.notes_entry.get()
        }
        
        self.dialog.destroy()


class PasteGoodsDialog:
    def __init__(self, parent, item_data):
        self.result = None
        self.item_data = item_data
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("粘贴商品")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置最小窗口大小
        self.dialog.minsize(600, 450)
        
        self.create_widgets()
        
        # 让窗口自适应内容大小并居中
        self.dialog.update_idletasks()
        width = max(600, self.dialog.winfo_reqwidth())
        height = max(450, self.dialog.winfo_reqheight())
        
        # 获取屏幕尺寸并计算居中位置
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置焦点
        self.text_area.focus_set()
    
    def create_widgets(self):
        """创建控件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        ttk.Label(main_frame, text="请粘贴商品数据（格式：商品名称\\t数量\\t单价，每行一个商品）:").pack(anchor=tk.W, pady=(0, 10))
        
        # 文本框
        self.text_area = tk.Text(main_frame, height=20, width=70)
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 示例
        example_text = "瘦肉\t5\t20\n莲蓉包\t6\t2\n猪头皮\t1\t0"
        self.text_area.insert(tk.END, example_text)
        
        # 按钮 - 固定在窗口底部
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 按钮居中显示
        button_container = ttk.Frame(button_frame)
        button_container.pack(anchor=tk.CENTER)
        
        ttk.Button(button_container, text="解析并添加", command=self.parse_and_add).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_container, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)
    
    def parse_and_add(self):
        """解析并添加商品"""
        text = self.text_area.get(1.0, tk.END).strip()
        if not text:
            messagebox.showwarning("警告", "请输入商品数据")
            return
        
        goods_list = []
        lines = text.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            parts = line.split('\t')
            if len(parts) < 3:
                messagebox.showerror("错误", f"第{line_num}行格式错误，应为：商品名称\\t数量\\t单价")
                return
            
            name = parts[0].strip()
            quantity = parts[1].strip()
            price = parts[2].strip()
            
            # 获取单位
            unit = ''
            if name in self.item_data:
                unit = self.item_data[name].get('unit', '')
            
            goods_list.append({
                'name': name,
                'quantity': quantity,
                'price': price,
                'unit': unit,
                'batchNo': '',
                'productionDate': '',
                'expireDate': '',
                'notes': ''
            })
        
        if goods_list:
            self.result = goods_list
            self.dialog.destroy()
        else:
            messagebox.showwarning("警告", "没有解析到有效的商品数据")


class BatchPasteTablesDialog:
    def __init__(self, parent, default_config, predefined_options, item_data):
        self.result = None
        self.default_config = default_config
        self.predefined_options = predefined_options
        self.item_data = item_data
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("批量粘贴表格")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置最小窗口大小
        self.dialog.minsize(800, 600)
        
        self.create_widgets()
        
        # 让窗口自适应内容大小并居中
        self.dialog.update_idletasks()
        width = max(800, self.dialog.winfo_reqwidth())
        height = max(600, self.dialog.winfo_reqheight())
        
        # 获取屏幕尺寸并计算居中位置
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置焦点
        self.text_area.focus_set()
    
    def create_widgets(self):
        """创建控件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 说明文字
        instruction_text = """请粘贴表格数据，格式如实例.md中的格式。
程序将自动解析所有表格（忽略总单），支持并排表格格式，提取以下信息：
- 日期：从标题中提取（如2025.06.10），保持原有日期但使用当前系统时间
- 仓库：根据备注（非城区特困长者、员工、城区特困长者、自费长者）映射到仓库ID
- 商品：名称及规格、数量、单价（忽略单位和金额）
- 表格名称：仓库名称 + 日期
- 描述和备注：自动留空
- 支持识别同一行中的多个表格（左右并排格式）"""
        
        ttk.Label(main_frame, text=instruction_text, justify=tk.LEFT).pack(anchor=tk.W, pady=(0, 10))
        
        # 仓库映射配置
        mapping_frame = ttk.LabelFrame(main_frame, text="仓库备注映射配置", padding=10)
        mapping_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.warehouse_mappings = {}
        warehouse_notes = ["非城区特困长者", "员工", "城区特困长者", "自费长者"]
        
        for i, note in enumerate(warehouse_notes):
            row = i // 2
            col = (i % 2) * 2

            ttk.Label(mapping_frame, text=f"{note}:").grid(row=row, column=col, sticky=tk.W, padx=5, pady=5)

            var = tk.StringVar()
            combo = ttk.Combobox(mapping_frame, textvariable=var, width=20, state="readonly")
            if 'entity.storeroom.id' in self.predefined_options:
                storeroom_options = list(self.predefined_options['entity.storeroom.id'].values())
                combo['values'] = storeroom_options
                # 智能匹配最相似的仓库名称
                if storeroom_options:
                    best_match = self.find_best_warehouse_match(note, storeroom_options)
                    var.set(best_match)
            combo.grid(row=row, column=col+1, padx=5, pady=5, sticky=tk.W)

            self.warehouse_mappings[note] = var
        
        # 文本框
        ttk.Label(main_frame, text="请粘贴表格数据:").pack(anchor=tk.W, pady=(10, 5))
        
        # 文本框和滚动条容器
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        self.text_area = tk.Text(text_frame, height=25, width=100)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.text_area.yview)
        self.text_area.configure(yscrollcommand=scrollbar.set)
        
        self.text_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮 - 固定在窗口底部
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
        
        # 按钮居中显示
        button_container = ttk.Frame(button_frame)
        button_container.pack(anchor=tk.CENTER)
        
        ttk.Button(button_container, text="解析并添加", command=self.parse_and_add).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_container, text="取消", command=self.dialog.destroy).pack(side=tk.LEFT)

    def find_best_warehouse_match(self, warehouse_note, storeroom_options):
        """智能匹配最相似的仓库名称"""
        # 计算每个仓库选项的匹配分数
        best_match = storeroom_options[0]  # 默认第一个
        best_score = 0

        for option in storeroom_options:
            score = 0

            # 完全匹配得最高分
            if warehouse_note == option.replace("仓库", ""):
                score += 1000

            # 精确包含匹配
            if warehouse_note in option:
                score += 500

            # 特殊处理：城区 vs 非城区
            if warehouse_note == "城区特困长者":
                if "城区特困长者" in option and "非城区" not in option:
                    score += 200
                elif "非城区" in option:
                    score = 0  # 完全排除非城区选项

            elif warehouse_note == "非城区特困长者":
                if "非城区特困长者" in option:
                    score += 200
                elif "城区特困长者" in option and "非城区" not in option:
                    score = 0  # 完全排除城区选项

            # 其他关键词匹配
            if "员工" in warehouse_note and "员工" in option:
                score += 100

            if "自费" in warehouse_note and "自费" in option:
                score += 100

            if score > best_score:
                best_score = score
                best_match = option

        return best_match


    
    def parse_and_add(self):
        """解析并添加表格"""
        text = self.text_area.get(1.0, tk.END).strip()
        if not text:
            messagebox.showwarning("警告", "请输入表格数据")
            return

        try:
            # 解析表格数据
            tables = self.parse_tables_data(text)
            if not tables:
                messagebox.showwarning("警告", "没有解析到有效的表格数据")
                return

            # 检查缺失的物品
            self._check_missing_items_in_tables(tables)

        except Exception as e:
            messagebox.showerror("错误", f"解析数据时出现错误：{str(e)}")

    def _check_missing_items_in_tables(self, tables):
        """检查表格中缺失的物品并显示匹配窗口"""
        try:
            # 收集所有表格中的物品名称
            all_items = []
            for table in tables:
                for goods in table.get('goods', []):
                    item_name = goods.get('name', '').strip()
                    if item_name:
                        all_items.append(item_name)

            if not all_items:
                self.result = tables
                self.dialog.destroy()
                return

            # 找出缺失的物品
            missing_items = ItemMatcher.find_missing_items(all_items, self.item_data)

            if missing_items:
                print(f"发现 {len(missing_items)} 个物品在item.json中不存在: {', '.join(missing_items)}")

                # 显示物品匹配窗口
                def on_reconvert(replacements):
                    print(f"用户选择了 {len(replacements)} 个替代物品，开始应用替换...")
                    self._apply_replacements_and_finish(tables, replacements)

                ItemMatcher.show_matcher_window(self.dialog, missing_items, self.item_data, on_reconvert)
            else:
                print("所有物品都在item.json中存在")
                self.result = tables
                self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"检查缺失物品时出错: {str(e)}")

    def _apply_replacements_and_finish(self, tables, replacements):
        """应用物品替换并完成添加"""
        try:
            # 应用替换到所有表格
            for table in tables:
                for goods in table.get('goods', []):
                    original_name = goods.get('name', '')
                    if original_name in replacements:
                        replacement_name = replacements[original_name]
                        goods['name'] = replacement_name
                        print(f"替换物品: {original_name} -> {replacement_name}")

                        # 更新单位信息
                        if replacement_name in self.item_data:
                            goods['unit'] = self.item_data[replacement_name].get('unit', goods.get('unit', ''))

            self.result = tables
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"应用物品替换时出错: {str(e)}")
    
    def parse_tables_data(self, text):
        """解析表格数据"""
        tables = []
        lines = text.split('\n')

        # 查找所有表格标题行，并识别每行中的所有表格
        table_headers = []
        for i, line in enumerate(lines):
            # 匹配表格标题格式：汕尾城区颐养院送货单（2025.06.10）（备注）
            # 使用findall来找到一行中的所有匹配项
            matches = re.findall(r'汕尾城区颐养院送货单（(\d{4}\.\d{2}\.\d{2})）（([^）]+)）', line)
            for match in matches:
                date_str = match[0]
                warehouse_note = match[1]
                if '总单' not in warehouse_note:  # 忽略总单
                    table_headers.append({
                        'line_index': i,
                        'date': date_str,
                        'warehouse_note': warehouse_note,
                        'line_content': line  # 保存整行内容用于判断位置
                    })

        # 按行分组表格标题
        grouped_headers = {}
        for header in table_headers:
            line_idx = header['line_index']
            if line_idx not in grouped_headers:
                grouped_headers[line_idx] = []
            grouped_headers[line_idx].append(header)

        # 解析每个表格
        for line_idx in sorted(grouped_headers.keys()):
            headers_in_line = grouped_headers[line_idx]

            # 确定表格数据范围
            start_line = line_idx + 1
            end_line = len(lines)

            # 找到下一个表格标题行作为结束位置
            next_line_idx = None
            for next_idx in sorted(grouped_headers.keys()):
                if next_idx > line_idx:
                    next_line_idx = next_idx
                    break
            if next_line_idx is not None:
                end_line = next_line_idx

            # 获取表格数据行
            table_lines = lines[start_line:end_line]

            # 为每个表格解析数据
            for header in headers_in_line:
                try:
                    # 判断表格在左侧还是右侧
                    line_content = header['line_content']
                    warehouse_note = header['warehouse_note']

                    # 通过查找表格标题在行中的位置来判断是左侧还是右侧表格
                    title_pattern = f'汕尾城区颐养院送货单（{header["date"]}）（{warehouse_note}）'
                    title_pos = line_content.find(title_pattern)
                    is_left_table = title_pos < len(line_content) // 2

                    # 解析商品数据
                    goods = self.parse_goods_from_lines(table_lines, warehouse_note, is_left_table)

                    if goods:  # 只有当有商品数据时才创建表格
                        # 转换日期格式：保持原有日期，但使用当前系统时间
                        date_obj = datetime.strptime(header['date'], '%Y.%m.%d')
                        current_time = datetime.now()
                        # 组合原有日期和当前时间
                        formatted_date = f"{date_obj.strftime('%Y-%m-%d')} {current_time.strftime('%H:%M:%S')}"

                        # 获取仓库ID
                        storeroom_id = self.get_storeroom_id(warehouse_note)

                        # 创建表格配置
                        table_config = {
                            'name': f"{warehouse_note} {header['date']}",
                            'description': "",  # 描述留空
                            'config': {
                                'entity.intoKey.id': self.default_config.get('entity.intoKey.id', ''),
                                'entity.storeroom.id': storeroom_id,
                                'entity.operatorUser': self.default_config.get('entity.operatorUser', ''),
                                'entity.intoDate': formatted_date,
                                'entity.deliveryNnoteNo': '',
                                'entity.notes': ""  # 备注留空
                            },
                            'goods': goods
                        }

                        tables.append(table_config)

                except Exception as e:
                    print(f"解析表格 {header['warehouse_note']} 时出错: {str(e)}")
                    continue

        return tables
    
    def parse_goods_from_lines(self, lines, warehouse_note, is_left_table=True):
        """从行数据中解析商品"""
        goods = []

        # 查找数据开始行（包含"名称及规格"的下一行）
        data_start = -1
        for i, line in enumerate(lines):
            if '名称及规格' in line and '数量' in line and '单价' in line:
                data_start = i + 1
                break

        if data_start == -1:
            return goods

        # 解析商品数据
        for line in lines[data_start:]:
            line = line.strip()
            if not line or '合计金额' in line or '送货人' in line:
                break

            # 分割数据（使用制表符分割）
            parts = line.split('\t')

            # 根据表格位置确定使用哪一组数据
            if is_left_table:
                # 左侧表格：索引 0,1,2,3,4
                if len(parts) >= 5:
                    name = parts[0].strip()
                    unit = parts[1].strip()
                    quantity = parts[2].strip()
                    price = parts[3].strip()
                else:
                    continue
            else:
                # 右侧表格：寻找右侧数据的起始位置
                # 通常在索引5之后开始，但需要跳过空白列
                right_start = -1
                for j in range(5, len(parts)):
                    if parts[j].strip():
                        right_start = j
                        break

                if right_start != -1 and right_start + 3 < len(parts):
                    name = parts[right_start].strip()
                    unit = parts[right_start + 1].strip()
                    quantity = parts[right_start + 2].strip()
                    price = parts[right_start + 3].strip()
                else:
                    continue

            # 跳过空行或无效数据
            if not name or not quantity or not price:
                continue

            # 验证数量和价格是否为数字
            try:
                float(quantity)
                float(price)
            except ValueError:
                continue

            # 获取商品单位（从item_data中获取，如果没有则使用解析出的单位）
            item_unit = ''
            if name in self.item_data:
                item_unit = self.item_data[name].get('unit', unit)
            else:
                item_unit = unit

            goods.append({
                'name': name,
                'quantity': quantity,
                'price': price,
                'unit': item_unit,
                'batchNo': '',
                'productionDate': '',
                'expireDate': '',
                'notes': ''
            })

        return goods
    
    def get_storeroom_id(self, warehouse_note):
        """根据仓库备注获取仓库ID"""
        if warehouse_note in self.warehouse_mappings:
            storeroom_display = self.warehouse_mappings[warehouse_note].get()
            if storeroom_display and 'entity.storeroom.id' in self.predefined_options:
                # 根据显示名称找到对应的ID
                for key, value in self.predefined_options['entity.storeroom.id'].items():
                    if value == storeroom_display:
                        return key
        
        # 如果没有找到映射，返回默认仓库ID
        return self.default_config.get('entity.storeroom.id', '')