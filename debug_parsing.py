#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试解析逻辑
"""

def debug_line_by_line():
    """逐行调试解析逻辑"""
    print("=== 逐行调试解析逻辑 ===")
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取员工表格部分（第25-50行）
    employee_lines = []
    for i in range(25, 50):  # 第26-50行
        if i < len(lines):
            employee_lines.append(lines[i].rstrip('\n'))
    
    print("逐行分析员工表格数据:")
    
    # 查找数据开始行
    data_start = -1
    for i, line in enumerate(employee_lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            print(f"数据开始行: {i+1}")
            break
    
    if data_start == -1:
        print("未找到数据开始行")
        return
    
    print("\n解析每一行:")
    for i, line in enumerate(employee_lines[data_start:], data_start):
        line = line.strip()
        if not line or '合计金额' in line or '送货人' in line:
            print(f"行{i+1}: 结束行 - {repr(line)}")
            break
        
        parts = line.split('\t')
        print(f"\n行{i+1}: {len(parts)}个部分")
        
        # 显示所有部分
        for j, part in enumerate(parts):
            print(f"  [{j}]: '{part}'")
        
        # 分析左侧表格
        if len(parts) >= 5:
            left_name = parts[0].strip()
            left_unit = parts[1].strip()
            left_quantity = parts[2].strip()
            left_price = parts[3].strip()
            
            print(f"  左侧数据: 名称='{left_name}', 单位='{left_unit}', 数量='{left_quantity}', 价格='{left_price}'")
            
            if left_name and left_quantity:
                try:
                    float(left_quantity)
                    print(f"  ✅ 左侧有效商品: {left_name}")
                except:
                    print(f"  ❌ 左侧数量无效: {left_quantity}")
            else:
                print(f"  ⚪ 左侧无商品数据")
        
        # 分析右侧表格
        if len(parts) >= 11:
            right_name = parts[7].strip()
            right_unit = parts[8].strip()
            right_quantity = parts[9].strip()
            right_price = parts[10].strip()
            
            print(f"  右侧数据: 名称='{right_name}', 单位='{right_unit}', 数量='{right_quantity}', 价格='{right_price}'")
            
            if right_name and right_quantity:
                try:
                    float(right_quantity)
                    print(f"  ✅ 右侧有效商品: {right_name}")
                except:
                    print(f"  ❌ 右侧数量无效: {right_quantity}")
            else:
                print(f"  ⚪ 右侧无商品数据")

def test_corrected_parsing():
    """测试修正后的解析逻辑"""
    print("\n=== 测试修正后的解析逻辑 ===")
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取员工表格部分（第25-50行）
    employee_lines = []
    for i in range(25, 50):
        if i < len(lines):
            employee_lines.append(lines[i].rstrip('\n'))
    
    # 解析左侧表格（员工）
    print("解析左侧表格（员工）:")
    left_goods = []
    
    # 查找数据开始行
    data_start = -1
    for i, line in enumerate(employee_lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            break
    
    if data_start != -1:
        for line in employee_lines[data_start:]:
            line = line.strip()
            if not line or '合计金额' in line or '送货人' in line:
                break
            
            parts = line.split('\t')
            
            # 左侧表格：索引 0,1,2,3,4
            if len(parts) >= 5:
                name = parts[0].strip()
                unit = parts[1].strip()
                quantity = parts[2].strip()
                price = parts[3].strip()
                
                # 只有当左侧确实有商品名称和数量时才处理
                if name and quantity:
                    try:
                        quantity_float = float(quantity)
                        price_float = float(price)
                        if quantity_float > 0:
                            left_goods.append({
                                'name': name,
                                'quantity': quantity,
                                'price': price,
                                'unit': unit
                            })
                            print(f"  添加: {name} - {quantity} {unit}")
                    except ValueError:
                        print(f"  跳过无效数据: {name} - {quantity}")
    
    print(f"\n左侧表格（员工）共解析出 {len(left_goods)} 个商品:")
    for goods in left_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']}")
    
    # 解析右侧表格（城区特困长者）
    print("\n解析右侧表格（城区特困长者）:")
    right_goods = []
    
    if data_start != -1:
        for line in employee_lines[data_start:]:
            line = line.strip()
            if not line or '合计金额' in line or '送货人' in line:
                break
            
            parts = line.split('\t')
            
            # 右侧表格：索引 7,8,9,10
            if len(parts) >= 11:
                name = parts[7].strip()
                unit = parts[8].strip()
                quantity = parts[9].strip()
                price = parts[10].strip()
                
                # 只有当右侧确实有商品名称和数量时才处理
                if name and quantity:
                    try:
                        quantity_float = float(quantity)
                        price_float = float(price)
                        if quantity_float > 0:
                            right_goods.append({
                                'name': name,
                                'quantity': quantity,
                                'price': price,
                                'unit': unit
                            })
                            print(f"  添加: {name} - {quantity} {unit}")
                    except ValueError:
                        print(f"  跳过无效数据: {name} - {quantity}")
    
    print(f"\n右侧表格（城区特困长者）共解析出 {len(right_goods)} 个商品:")
    for goods in right_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']}")
    
    # 验证结果
    left_names = [goods['name'] for goods in left_goods]
    right_names = [goods['name'] for goods in right_goods]
    
    print(f"\n验证结果:")
    print(f"左侧商品: {left_names}")
    print(f"右侧商品: {right_names}")
    
    # 检查是否有重复
    overlap = set(left_names) & set(right_names)
    if overlap:
        print(f"⚠️ 发现重复商品: {overlap}")
    
    # 检查员工表格是否包含不应该有的商品
    unexpected_in_employee = ["排骨", "豆干", "苦瓜", "绿豆", "白糖", "热狗"]
    found_unexpected = [item for item in left_names if item in unexpected_in_employee]
    
    if found_unexpected:
        print(f"❌ 员工表格中发现不应该存在的商品: {found_unexpected}")
    else:
        print(f"✅ 员工表格没有不应该存在的商品")

if __name__ == "__main__":
    debug_line_by_line()
    test_corrected_parsing()
