#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的批量粘贴表格功能
"""

import re
from datetime import datetime

def test_parse_tables():
    """测试解析表格数据"""
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        text = f.read()
    
    lines = text.split('\n')
    
    # 查找所有表格标题行，并识别每行中的所有表格
    table_headers = []
    for i, line in enumerate(lines):
        # 匹配表格标题格式：汕尾城区颐养院送货单（2025.06.10）（备注）
        # 使用findall来找到一行中的所有匹配项
        matches = re.findall(r'汕尾城区颐养院送货单（(\d{4}\.\d{2}\.\d{2})）（([^）]+)）', line)
        for match in matches:
            date_str = match[0]
            warehouse_note = match[1]
            if '总单' not in warehouse_note:  # 忽略总单
                table_headers.append({
                    'line_index': i,
                    'date': date_str,
                    'warehouse_note': warehouse_note,
                    'line_content': line  # 保存整行内容用于判断位置
                })
    
    print(f"找到 {len(table_headers)} 个表格标题:")
    for i, header in enumerate(table_headers):
        print(f"{i+1}. 行{header['line_index']+1}: {header['warehouse_note']} ({header['date']})")
        
        # 判断表格在左侧还是右侧
        line_content = header['line_content']
        warehouse_note = header['warehouse_note']
        
        # 通过查找表格标题在行中的位置来判断是左侧还是右侧表格
        title_pattern = f'汕尾城区颐养院送货单（{header["date"]}）（{warehouse_note}）'
        title_pos = line_content.find(title_pattern)
        is_left_table = title_pos < len(line_content) // 2
        
        print(f"   位置: {'左侧' if is_left_table else '右侧'} (标题位置: {title_pos}, 行长度: {len(line_content)})")
        
        # 测试日期时间格式化
        date_obj = datetime.strptime(header['date'], '%Y.%m.%d')
        current_time = datetime.now()
        formatted_date = f"{date_obj.strftime('%Y-%m-%d')} {current_time.strftime('%H:%M:%S')}"
        print(f"   入库时间: {formatted_date}")
    
    # 按行分组表格标题
    grouped_headers = {}
    for header in table_headers:
        line_idx = header['line_index']
        if line_idx not in grouped_headers:
            grouped_headers[line_idx] = []
        grouped_headers[line_idx].append(header)
    
    print(f"\n按行分组后:")
    for line_idx in sorted(grouped_headers.keys()):
        headers_in_line = grouped_headers[line_idx]
        print(f"行{line_idx+1}: {len(headers_in_line)}个表格")
        for header in headers_in_line:
            print(f"  - {header['warehouse_note']}")
    
    # 测试商品数据解析
    print(f"\n测试商品数据解析:")
    total_tables = 0
    total_goods = 0
    
    for line_idx in sorted(grouped_headers.keys()):
        headers_in_line = grouped_headers[line_idx]
        
        # 确定表格数据范围
        start_line = line_idx + 1
        end_line = len(lines)
        
        # 找到下一个表格标题行作为结束位置
        next_line_idx = None
        for next_idx in sorted(grouped_headers.keys()):
            if next_idx > line_idx:
                next_line_idx = next_idx
                break
        if next_line_idx is not None:
            end_line = next_line_idx
        
        # 获取表格数据行
        table_lines = lines[start_line:end_line]
        
        print(f"\n行{line_idx+1}的表格数据范围: {start_line+1}-{end_line}")
        
        # 为每个表格解析数据
        for header in headers_in_line:
            warehouse_note = header['warehouse_note']
            
            # 判断表格在左侧还是右侧
            line_content = header['line_content']
            title_pattern = f'汕尾城区颐养院送货单（{header["date"]}）（{warehouse_note}）'
            title_pos = line_content.find(title_pattern)
            is_left_table = title_pos < len(line_content) // 2
            
            print(f"\n解析表格: {warehouse_note} ({'左侧' if is_left_table else '右侧'})")
            
            # 解析商品数据
            goods = parse_goods_from_lines(table_lines, warehouse_note, is_left_table)
            print(f"找到 {len(goods)} 个商品:")
            for goods_item in goods:
                print(f"  - {goods_item['name']}: {goods_item['quantity']} {goods_item['unit']} × {goods_item['price']}元")
            
            if goods:
                total_tables += 1
                total_goods += len(goods)
    
    print(f"\n总结:")
    print(f"- 成功解析了 {total_tables} 个表格（应该是4个）")
    print(f"- 总共找到 {total_goods} 个商品")
    print(f"- 修复状态: {'✅ 成功' if total_tables == 4 else '❌ 失败'}")
    
    return total_tables == 4

def parse_goods_from_lines(lines, warehouse_note, is_left_table=True):
    """从行数据中解析商品"""
    goods = []
    
    # 查找数据开始行（包含"名称及规格"的下一行）
    data_start = -1
    for i, line in enumerate(lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            break
    
    if data_start == -1:
        return goods
    
    # 解析商品数据
    for line in lines[data_start:]:
        line = line.strip()
        if not line or '合计金额' in line or '送货人' in line:
            break
        
        # 分割数据（使用制表符分割）
        parts = line.split('\t')
        
        # 根据表格位置确定使用哪一组数据
        if is_left_table:
            # 左侧表格：索引 0,1,2,3,4
            if len(parts) >= 5:
                name = parts[0].strip()
                unit = parts[1].strip()
                quantity = parts[2].strip()
                price = parts[3].strip()
            else:
                continue
        else:
            # 右侧表格：寻找右侧数据的起始位置
            # 通常在索引5之后开始，但需要跳过空白列
            right_start = -1
            for j in range(5, len(parts)):
                if parts[j].strip():
                    right_start = j
                    break
            
            if right_start != -1 and right_start + 3 < len(parts):
                name = parts[right_start].strip()
                unit = parts[right_start + 1].strip()
                quantity = parts[right_start + 2].strip()
                price = parts[right_start + 3].strip()
            else:
                continue
        
        # 跳过空行或无效数据
        if not name or not quantity or not price:
            continue
        
        # 验证数量和价格是否为数字
        try:
            float(quantity)
            float(price)
        except ValueError:
            continue
        
        goods.append({
            'name': name,
            'quantity': quantity,
            'price': price,
            'unit': unit,
            'batchNo': '',
            'productionDate': '',
            'expireDate': '',
            'notes': ''
        })
    
    return goods

if __name__ == "__main__":
    success = test_parse_tables()
    if success:
        print("\n🎉 批量粘贴表格功能修复成功！现在可以正确识别4个表格了。")
    else:
        print("\n❌ 还有问题需要进一步修复。")
