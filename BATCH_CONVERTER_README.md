# 批量转换功能使用说明

## 功能概述

批量转换功能允许您配置多个表格，每个表格可以包含不同的商品和配置信息，然后一次性将所有表格转换为JSON文件。

## 主要特性

### 1. 配置管理
- **默认配置**: 设置所有表格的默认基本信息
- **表格特定配置**: 每个表格可以单独设置基本信息
- **配置文件**: 所有配置保存在 `batch_config.json` 文件中

### 2. 表格管理
- **添加表格**: 创建新的表格配置
- **删除表格**: 删除不需要的表格
- **编辑表格**: 修改现有表格的配置和商品

### 3. 商品配置
- **商品验证**: 自动检查商品是否在 `item.json` 中存在
- **错误提示**: 不存在的商品会用红色 ❌ 标记
- **Excel粘贴**: 支持直接粘贴Excel格式的商品数据
- **自动填充**: 选择商品名称后自动填充单位信息

### 4. 预定义选项
- **入库类型**: 可配置的入库类型选项
- **仓库ID**: 可配置的仓库选项
- **操作员**: 可配置的操作员选项
- **实际值和备注**: 每个选项都有实际值和备注信息

## 使用步骤

### 1. 打开批量转换器
在主界面点击 "批量转换" 按钮

### 2. 配置默认信息
在 "默认配置" 页面设置：
- 入库类型
- 仓库ID
- 操作员
- 制造商信息

### 3. 配置表格
在 "表格配置" 页面：
1. 点击 "添加表格" 创建新表格
2. 填写表格名称和描述
3. 设置入库日期（默认为当前时间）
4. 配置商品信息

### 4. 添加商品
有三种方式添加商品：

#### 方式一：单个添加
1. 点击 "添加商品"
2. 选择或输入商品名称
3. 填写数量、单价等信息

#### 方式二：Excel粘贴
1. 点击 "粘贴商品"
2. 粘贴Excel格式的数据（格式：商品名称\t数量\t单价）
3. 点击 "解析并添加"

#### 方式三：批量输入
在粘贴对话框中输入多行数据，每行格式为：
```
商品名称    数量    单价
瘦肉       5      20
莲蓉包     6      2
```

### 5. 开始转换
1. 保存配置
2. 点击 "开始批量转换"
3. 转换完成后，JSON文件将保存在 `out` 文件夹中

## 配置文件说明

### batch_config.json 结构
```json
{
  "default_config": {
    "entity.intoKey.id": "入库类型",
    "entity.storeroom.id": "仓库ID",
    "entity.operatorUser": "操作员",
    "entity.manuf.id": "制造商ID",
    "entity.manufLinkTellp": "制造商电话",
    "entity.manufAddress1": "制造商地址"
  },
  "tables": [
    {
      "name": "表格名称",
      "description": "表格描述",
      "config": {
        "entity.intoDate": "入库日期",
        "entity.deliveryNnoteNo": "送货单号",
        "entity.notes": "备注"
      },
      "goods": [
        {
          "name": "商品名称",
          "quantity": "数量",
          "price": "单价",
          "unit": "单位",
          "batchNo": "批次号",
          "productionDate": "生产日期",
          "expireDate": "过期日期",
          "notes": "备注"
        }
      ]
    }
  ],
  "predefined_options": {
    "entity.intoKey.id": {
      "实际值": "显示备注"
    }
  }
}
```

## 输出文件

转换完成后，每个表格会生成一个独立的JSON文件，保存在 `out` 文件夹中：
- 文件名格式：`{表格名称}.json`
- 如果 `out` 文件夹不存在，会自动创建

## 注意事项

1. **商品验证**: 确保所有商品都在 `item.json` 中存在，否则会显示红色警告
2. **数据格式**: 数量和单价必须是有效的数字
3. **日期格式**: 日期格式为 `YYYY-MM-DD HH:MM:SS`
4. **配置保存**: 记得在转换前保存配置
5. **文件备份**: 建议定期备份 `batch_config.json` 文件

## 故障排除

### 常见问题

1. **商品不存在**: 
   - 检查 `item.json` 文件
   - 使用 "编辑物品" 功能添加缺失的商品

2. **转换失败**:
   - 检查数量和单价是否为有效数字
   - 确保表格名称不为空
   - 检查日期格式是否正确

3. **配置丢失**:
   - 检查 `batch_config.json` 文件是否存在
   - 重新配置并保存

### 技术支持

如果遇到问题，请检查：
1. 所有必需的文件是否存在
2. 配置文件格式是否正确
3. 商品数据是否完整