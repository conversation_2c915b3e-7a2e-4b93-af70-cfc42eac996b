# Excel转JSON工具

## 项目简介

这是一个用于将Excel文件转换为特定格式JSON的工具，主要用于数据导入系统。该工具提供了友好的图形用户界面，支持Excel数据预览、自动映射和批量转换功能。

## 功能特点

- **直观的用户界面**：简洁明了的界面设计，易于操作
- **Excel数据预览**：支持在转换前预览Excel数据内容
- **自动字段映射**：根据预定义的映射规则自动转换数据
- **商品信息自动填充**：根据商品名称自动填充ID和编号
- **智能物品匹配**：当Excel中的物品在item.json中不存在时，自动弹出匹配窗口，支持选择相似物品作为替代
- **自动相似度匹配**：基于字符串相似度算法，自动推荐最相似的替代物品
- **重新转换功能**：选择替代物品后可一键重新转换，无需重新操作
- **数据验证**：转换过程中进行数据格式和完整性验证
- **日志记录**：详细记录转换过程和可能出现的错误
- **示例模板**：提供标准Excel模板，方便用户按格式填写数据

## 安装说明

### 方法一：直接使用可执行文件

1. 从`dist`目录下载`Excel转JSON工具.exe`文件
2. 双击运行即可，无需安装

### 方法二：从源码运行

1. 确保已安装Python 3.8或更高版本
2. 安装依赖库：
   ```
   pip install -r requirements.txt
   ```
3. 运行主程序：
   ```
   python main.py
   ```

## 使用指南

### 基本使用流程

1. **选择Excel文件**：点击"浏览"按钮选择要转换的Excel文件
2. **预览数据**：点击"预览数据"按钮查看Excel内容
3. **设置保存位置**：选择JSON文件的保存位置
4. **开始转换**：点击"开始转换"按钮
5. **物品匹配**（如果需要）：如果Excel中有物品在item.json中不存在，会自动弹出匹配窗口
6. **查看结果**：转换完成后会显示JSON预览窗口

### 智能物品匹配功能

当Excel表格中的物品名称在item.json文件中找不到对应记录时，系统会自动弹出物品匹配窗口，帮助您选择合适的替代物品。

#### 匹配窗口功能说明

1. **自动推荐**：系统会基于字符串相似度算法，为每个缺失的物品推荐最相似的替代物品
2. **手动选择**：您可以从下拉列表中手动选择合适的替代物品
3. **跳过物品**：选择"不替换"可以跳过某个物品，该物品将不会被包含在最终的JSON中
4. **一键匹配**：点击"自动匹配最相似"按钮可以为所有缺失物品自动选择最相似的替代物品
5. **重新转换**：选择完替代物品后，点击"使用选择的替代物品重新转换"即可完成转换

#### 使用技巧

- **相似度匹配**：系统会优先推荐名称最相似的物品，通常推荐的第一个选项是最佳匹配
- **批量处理**：如果有多个缺失物品，建议先使用"自动匹配最相似"功能，然后再手动调整个别不合适的匹配
- **预览关闭**：为避免混淆，在显示物品匹配窗口时，系统会自动关闭JSON预览窗口
- **重新转换**：选择替代物品后的重新转换会保留所有原始设置，无需重新选择文件和路径

### Excel文件格式要求

工具支持包含以下工作表的Excel文件：

1. **基本信息**工作表：包含订单的基本信息
2. **商品明细**工作表：包含商品的详细信息

#### 必需字段

- **商品名称**：必须与item.json中的物品名称匹配，或通过物品匹配功能选择替代物品
- **数量**：商品数量
- **单价**：商品单价
- **其他字段**：根据mapping.json配置文件的映射规则

### 配置文件说明

#### item.json
存储所有可用物品的信息，格式如下：
```json
{
  "物品名称": {
    "id": "物品ID",
    "no": "物品编号", 
    "unit": "单位"
  }
}
```

#### mapping.json
定义Excel字段到JSON字段的映射关系，控制数据转换规则。

### 常见问题

**Q: 为什么会出现物品匹配窗口？**
A: 当Excel中的物品名称在item.json文件中找不到完全匹配的记录时，系统会弹出匹配窗口帮助您选择替代物品。

**Q: 如何避免物品匹配问题？**
A: 确保Excel中的物品名称与item.json中的名称完全一致，包括空格、标点符号等。

**Q: 可以添加新物品到item.json吗？**
A: 可以，使用工具中的"物品编辑器"功能可以添加、编辑或删除item.json中的物品信息。

**Q: 重新转换会覆盖之前的文件吗？**
A: 是的，重新转换会覆盖之前选择的JSON文件路径中的文件。

### 技术支持

如果在使用过程中遇到问题，请检查：

1. Excel文件格式是否正确
2. item.json和mapping.json文件是否存在且格式正确
3. 查看日志窗口中的详细错误信息

### 更新日志

#### v2.0.0 (最新版本)
- 新增智能物品匹配功能
- 新增自动相似度匹配算法
- 新增重新转换功能
- 优化用户界面体验
- 改进错误处理和日志记录

#### v1.0.0
- 基础Excel转JSON功能
- 商品信息自动填充
- 数据预览功能
- 物品编辑器