#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Excel解析逻辑
"""

def parse_goods_from_lines_fixed(lines, warehouse_note, is_left_table=True):
    """修复后的商品解析函数"""
    goods = []

    # 查找数据开始行（包含"名称及规格"的下一行）
    data_start = -1
    for i, line in enumerate(lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            break

    if data_start == -1:
        return goods

    # 解析商品数据
    for line in lines[data_start:]:
        line = line.strip()
        if not line or '合计金额' in line or '送货人' in line:
            break

        # 分割数据（使用制表符分割）
        parts = line.split('\t')

        # 根据表格位置确定使用哪一组数据
        if is_left_table:
            # 左侧表格：索引 0,1,2,3,4
            if len(parts) >= 5:
                name = parts[0].strip()
                unit = parts[1].strip()
                quantity = parts[2].strip()
                price = parts[3].strip()
                
                # 只有当左侧确实有商品名称和数量时才处理
                if not name or not quantity:
                    continue
            else:
                continue
        else:
            # 右侧表格：固定从索引7开始（基于分析结果）
            if len(parts) >= 11:  # 确保有足够的列
                name = parts[7].strip()
                unit = parts[8].strip()
                quantity = parts[9].strip()
                price = parts[10].strip()
                
                # 只有当右侧确实有商品名称和数量时才处理
                if not name or not quantity:
                    continue
            else:
                continue

        # 验证数量和价格是否为数字，并且数量不为0
        try:
            quantity_float = float(quantity)
            price_float = float(price)
            if quantity_float <= 0:  # 跳过数量为0或负数的商品
                continue
        except ValueError:
            continue

        goods.append({
            'name': name,
            'quantity': quantity,
            'price': price,
            'unit': unit,
            'batchNo': '',
            'productionDate': '',
            'expireDate': '',
            'notes': ''
        })

    return goods

def test_employee_table_parsing():
    """测试员工表格解析"""
    print("=== 测试员工表格解析 ===")
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取员工表格部分（第25-50行）
    employee_lines = []
    for i in range(25, 50):  # 第26-50行
        if i < len(lines):
            employee_lines.append(lines[i].rstrip('\n'))
    
    print("员工表格原始数据:")
    for i, line in enumerate(employee_lines[:10]):  # 只显示前10行
        print(f"  {i+26}: {repr(line)}")
    
    # 解析左侧表格（员工）
    employee_goods = parse_goods_from_lines_fixed(employee_lines, "员工", is_left_table=True)
    
    print(f"\n员工表格解析结果（{len(employee_goods)}个商品）:")
    for goods in employee_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']} × {goods['price']}元")
    
    # 预期的员工商品（基于实例文件分析）
    expected_employee_items = [
        "肚肉", "瘦肉", "光鸡整只去爪", "包菜", "胡萝卜", "白菜", "芹葱蒜", "广霸米粉", "黄小米"
    ]
    
    actual_employee_items = [goods['name'] for goods in employee_goods]
    
    print(f"\n预期商品: {expected_employee_items}")
    print(f"实际商品: {actual_employee_items}")
    
    # 检查是否有不应该出现的商品
    unexpected_items = ["排骨", "豆干", "苦瓜", "绿豆", "白糖", "热狗"]
    found_unexpected = [item for item in actual_employee_items if item in unexpected_items]
    
    if found_unexpected:
        print(f"❌ 员工表格中发现不应该存在的商品: {found_unexpected}")
        return False
    elif set(actual_employee_items) == set(expected_employee_items):
        print("✅ 员工表格解析完全正确")
        return True
    else:
        missing = set(expected_employee_items) - set(actual_employee_items)
        extra = set(actual_employee_items) - set(expected_employee_items)
        print(f"⚠️ 员工表格解析部分正确，缺少: {missing}, 多余: {extra}")
        return len(found_unexpected) == 0  # 只要没有不应该存在的商品就算通过

def test_city_elderly_table_parsing():
    """测试城区特困长者表格解析"""
    print("\n=== 测试城区特困长者表格解析 ===")
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取城区特困长者表格部分（第25-50行，右侧）
    city_elderly_lines = []
    for i in range(25, 50):  # 第26-50行
        if i < len(lines):
            city_elderly_lines.append(lines[i].rstrip('\n'))
    
    # 解析右侧表格（城区特困长者）
    city_elderly_goods = parse_goods_from_lines_fixed(city_elderly_lines, "城区特困长者", is_left_table=False)
    
    print(f"城区特困长者表格解析结果（{len(city_elderly_goods)}个商品）:")
    for goods in city_elderly_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']} × {goods['price']}元")
    
    # 预期的城区特困长者商品（应该包含所有商品）
    expected_city_elderly_items = [
        "肚肉", "瘦肉", "排骨", "豆干", "光鸡整只去爪", "包菜", "胡萝卜", "白菜", 
        "苦瓜", "芹葱蒜", "广霸米粉", "绿豆", "黄小米", "白糖", "热狗"
    ]
    
    actual_city_elderly_items = [goods['name'] for goods in city_elderly_goods]
    
    print(f"\n预期商品: {expected_city_elderly_items}")
    print(f"实际商品: {actual_city_elderly_items}")
    
    if set(actual_city_elderly_items) == set(expected_city_elderly_items):
        print("✅ 城区特困长者表格解析完全正确")
        return True
    else:
        missing = set(expected_city_elderly_items) - set(actual_city_elderly_items)
        extra = set(actual_city_elderly_items) - set(expected_city_elderly_items)
        print(f"⚠️ 城区特困长者表格解析不完整，缺少: {missing}, 多余: {extra}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的Excel解析逻辑...\n")
    
    test1_passed = test_employee_table_parsing()
    test2_passed = test_city_elderly_table_parsing()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"员工表格解析: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"城区特困长者表格解析: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 Excel解析逻辑修复成功！")
        print("员工表格不再包含不应该存在的商品，城区特困长者表格包含完整的商品列表。")
    else:
        print("\n❌ 还有问题需要进一步修复。")

if __name__ == "__main__":
    main()
