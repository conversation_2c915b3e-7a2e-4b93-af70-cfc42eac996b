#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示增强配置功能
"""

import json

def demo_enhanced_config():
    """演示增强配置功能"""
    
    print("=== Excel转JSON工具 - 批量转换配置增强功能演示 ===\n")
    
    # 读取配置
    try:
        with open('batch_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except:
        print("无法读取配置文件")
        return
    
    print("1. 默认配置:")
    default_config = config['default_config']
    predefined_options = config['predefined_options']
    
    print(f"   入库类型: {default_config.get('entity.intoKey.id')} -> {predefined_options['entity.intoKey.id'].get(default_config.get('entity.intoKey.id'))}")
    print(f"   仓库: {default_config.get('entity.storeroom.id')} -> {predefined_options['entity.storeroom.id'].get(default_config.get('entity.storeroom.id'))}")
    print(f"   操作员: {default_config.get('entity.operatorUser')} -> {predefined_options['entity.operatorUser'].get(default_config.get('entity.operatorUser'))}")
    
    print("\n2. 可选配置项:")
    print("   入库类型选项:")
    for key, value in predefined_options['entity.intoKey.id'].items():
        print(f"     {key} -> {value}")
    
    print("   仓库选项:")
    for key, value in predefined_options['entity.storeroom.id'].items():
        print(f"     {key} -> {value}")
    
    print("   操作员选项:")
    for key, value in predefined_options['entity.operatorUser'].items():
        print(f"     {key} -> {value}")
    
    print("\n3. 功能说明:")
    print("   - 在添加表格或编辑表格时，可以为每个表格单独配置：")
    print("     * 入库类型（采购入库、退货入库、调拨入库）")
    print("     * 仓库（主仓库、副仓库）")
    print("     * 操作员（汕尾城区护理院、管理员）")
    print("   - 如果表格中没有配置这些项，将使用默认配置")
    print("   - 如果表格中配置了这些项，将优先使用表格配置")
    
    print("\n4. 使用方法:")
    print("   - 打开批量转换工具")
    print("   - 点击'添加表格'或'编辑表格'")
    print("   - 在配置信息区域，可以看到新增的下拉选择框：")
    print("     * 入库类型：选择采购入库、退货入库或调拨入库")
    print("     * 仓库：选择主仓库或副仓库")
    print("     * 操作员：选择汕尾城区护理院或管理员")
    print("   - 这些选项默认使用默认配置中的值")
    print("   - 可以根据需要修改为其他选项")
    print("   - 保存后，转换JSON时会使用表格特定的配置")
    
    print("\n5. 配置优先级:")
    print("   表格配置 > 默认配置")
    print("   即：如果表格中配置了某项，使用表格配置；否则使用默认配置")

if __name__ == "__main__":
    demo_enhanced_config()