# 物品编辑器模块
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
from goods_mapping import GoodsMapper

class ItemEditor:
    def __init__(self, parent, callback=None):
        # 创建编辑窗口
        self.window = tk.Toplevel(parent)
        self.window.title("物品数据编辑器")
        self.window.geometry("900x700")
        self.window.minsize(800, 600)
        
        # 设置图标
        try:
            self.window.iconbitmap(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'icon.ico'))
        except:
            pass
            
        # 定义颜色方案 - 更现代的配色
        self.primary_color = "#3f51b5"  # 主色调：靛蓝色
        self.secondary_color = "#f5f7fa"  # 次要色调：浅灰蓝色
        self.accent_color = "#5c6bc0"  # 强调色：亮靛蓝色
        self.text_color = "#2c3e50"  # 文本色：深蓝灰色
        self.success_color = "#4caf50"  # 成功色：绿色
        self.warning_color = "#ff9800"  # 警告色：橙色
        self.error_color = "#f44336"  # 错误色：红色
        self.hover_color = "#7986cb"  # 悬停色：浅靛蓝色
        
        # 创建样式对象
        style = ttk.Style()
        
        # 设置全局字体
        self.default_font = ('微软雅黑', 10)
        self.bold_font = ('微软雅黑', 10, 'bold')
        self.small_font = ('微软雅黑', 9)
        
        # 自定义按钮样式 - 添加圆角和阴影效果
        style.configure('Editor.TButton',
                        background=self.primary_color,
                        foreground="white",
                        padding=8,
                        font=self.bold_font,
                        relief="flat",
                        borderwidth=0)
        style.map('Editor.TButton',
                  background=[('active', self.hover_color), ('pressed', self.accent_color)],
                  foreground=[('active', 'white'), ('pressed', 'white')])
                  
        # 创建成功按钮样式
        style.configure('Success.TButton',
                        background=self.success_color,
                        foreground="white",
                        padding=8,
                        font=self.bold_font,
                        relief="flat",
                        borderwidth=0)
        style.map('Success.TButton',
                  background=[('active', '#66bb6a'), ('pressed', '#43a047')],
                  foreground=[('active', 'white'), ('pressed', 'white')])
        
        # 自定义标签框样式
        style.configure('Editor.TLabelframe', 
                        background=self.secondary_color,
                        bordercolor=self.primary_color)
        style.configure('Editor.TLabelframe.Label', 
                        font=('微软雅黑', 10, 'bold'),
                        foreground=self.primary_color,
                        background=self.secondary_color)
        
        # 自定义输入框样式
        style.configure('Editor.TEntry', 
                        fieldbackground="white",
                        bordercolor=self.primary_color)
        
        # 自定义树状视图样式
        style.configure("Editor.Treeview",
                        background="white",
                        foreground=self.text_color,
                        rowheight=25,
                        fieldbackground="white",
                        borderwidth=0,
                        font=("微软雅黑", 9))
        style.configure("Editor.Treeview.Heading",
                        background=self.primary_color,
                        foreground="white",
                        relief="flat",
                        font=("微软雅黑", 10, "bold"))
        style.map("Editor.Treeview",
                  background=[('selected', self.accent_color)],
                  foreground=[('selected', 'white')])
        
        # 设置回调函数，用于保存后通知主窗口重新加载数据
        self.callback = callback
        
        # 在__init__方法中，替换初始化商品映射器的部分
        # 初始化商品映射器
        self.goods_mapper = GoodsMapper()
        try:
            # 尝试从当前工作目录加载
            self.goods_data = self.goods_mapper.get_all_mappings()
        except Exception as e:
            print(f"从默认路径加载数据失败: {str(e)}")
            # 如果默认加载失败，尝试直接从当前工作目录加载item.json
            try:
                if os.path.exists('item.json'):
                    print("尝试从当前工作目录加载item.json")
                    with open('item.json', 'r', encoding='utf-8') as f:
                        self.goods_data = json.load(f)
                    print(f"成功从当前工作目录加载，共{len(self.goods_data)}条记录")
                else:
                    print("当前工作目录中不存在item.json，使用空数据")
                    self.goods_data = {}
            except Exception as load_error:
                print(f"加载item.json失败: {str(load_error)}")
                self.goods_data = {}
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.window, padding="20 20 20 20", style="Editor.TFrame")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 设置主框架样式
        style.configure("Editor.TFrame", background=self.secondary_color)
        
        # 创建分割窗口
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧物品列表框架
        self.list_frame = ttk.LabelFrame(self.paned_window, text="物品列表", padding=10, style="Editor.TLabelframe")
        self.paned_window.add(self.list_frame, weight=1)
        
        # 创建右侧编辑框架
        self.edit_frame = ttk.LabelFrame(self.paned_window, text="编辑物品", padding=10, style="Editor.TLabelframe")
        self.paned_window.add(self.edit_frame, weight=1)
        
        # 创建物品列表
        self._create_item_list()
        
        # 创建编辑表单
        self._create_edit_form()
        
        # 创建底部按钮区域
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 添加保存按钮
        self.save_button = ttk.Button(self.button_frame, text="保存更改", 
                                     command=self.save_changes, width=15)
        self.save_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加关闭按钮
        self.close_button = ttk.Button(self.button_frame, text="关闭", 
                                      command=self.window.destroy, width=15)
        self.close_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加搜索框
        self.search_frame = ttk.Frame(self.list_frame, style="Editor.TFrame")
        self.search_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace("w", self._filter_items)
        
        search_label = ttk.Label(self.search_frame, text="搜索:", font=("微软雅黑", 9, "bold"), foreground=self.primary_color)
        search_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var, width=20, style="Editor.TEntry")
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # 初始状态设置为禁用编辑区域
        self._disable_edit_form()
        
        # 加载数据
        self._load_items()
    
    def _create_item_list(self):
        """创建物品列表视图"""
        # 创建列表框架
        list_container = ttk.Frame(self.list_frame, style="Editor.TFrame")
        list_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建Treeview和滚动条
        columns = ("name", "id", "no", "unit")
        self.item_tree = ttk.Treeview(list_container, columns=columns, show="headings", 
                                     selectmode="browse", style="Editor.Treeview")
        
        # 设置列标题
        self.item_tree.heading("name", text="物品名称")
        self.item_tree.heading("id", text="ID")
        self.item_tree.heading("no", text="编号")
        self.item_tree.heading("unit", text="单位")
        
        # 设置列宽
        self.item_tree.column("name", width=150, minwidth=100)
        self.item_tree.column("id", width=250, minwidth=150)
        self.item_tree.column("no", width=100, minwidth=80)
        self.item_tree.column("unit", width=50, minwidth=50)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.item_tree.yview, 
                                 style="Editor.Vertical.TScrollbar")
        self.item_tree.configure(yscrollcommand=scrollbar.set)
        
        # 添加水平滑动条
        h_scrollbar = ttk.Scrollbar(list_container, orient=tk.HORIZONTAL, command=self.item_tree.xview, 
                                   style="Editor.Horizontal.TScrollbar")
        self.item_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # 设置滚动条样式
        style = ttk.Style()
        style.configure("Editor.Vertical.TScrollbar", 
                       background=self.secondary_color, 
                       troughcolor="white", 
                       arrowcolor=self.primary_color)
        style.configure("Editor.Horizontal.TScrollbar", 
                       background=self.secondary_color, 
                       troughcolor="white", 
                       arrowcolor=self.primary_color)
        
        # 布局 - 使用grid布局更精确控制位置
        self.item_tree.grid(row=0, column=0, sticky='nsew', padx=2, pady=2)
        scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # 配置网格权重
        list_container.grid_rowconfigure(0, weight=1)
        list_container.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.item_tree.bind("<<TreeviewSelect>>", self._on_item_select)
        
        # 添加操作按钮
        self.list_button_frame = ttk.Frame(self.list_frame, style="Editor.TFrame")
        self.list_button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.add_button = ttk.Button(self.list_button_frame, text="添加物品", 
                                    command=self._add_new_item, width=15, style="Editor.TButton")
        self.add_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.delete_button = ttk.Button(self.list_button_frame, text="删除物品", 
                                       command=self._delete_item, width=15, style="Editor.TButton")
        self.delete_button.pack(side=tk.LEFT)
    
    def _create_edit_form(self):
        """创建编辑表单"""
        # 创建表单框架
        form_frame = ttk.Frame(self.edit_frame, style="Editor.TFrame")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 物品名称
        name_label = ttk.Label(form_frame, text="物品名称:", font=("微软雅黑", 9, "bold"), foreground=self.primary_color)
        name_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=10)
        
        self.name_var = tk.StringVar()
        self.name_entry = ttk.Entry(form_frame, textvariable=self.name_var, width=30, style="Editor.TEntry")
        self.name_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=10)
        
        # ID
        id_label = ttk.Label(form_frame, text="ID:", font=("微软雅黑", 9, "bold"), foreground=self.primary_color)
        id_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=10)
        
        self.id_var = tk.StringVar()
        self.id_entry = ttk.Entry(form_frame, textvariable=self.id_var, width=40, style="Editor.TEntry")
        self.id_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=10)
        
        # 编号
        no_label = ttk.Label(form_frame, text="编号:", font=("微软雅黑", 9, "bold"), foreground=self.primary_color)
        no_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=10)
        
        self.no_var = tk.StringVar()
        self.no_entry = ttk.Entry(form_frame, textvariable=self.no_var, width=30, style="Editor.TEntry")
        self.no_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=10)
        
        # 单位
        unit_label = ttk.Label(form_frame, text="单位:", font=("微软雅黑", 9, "bold"), foreground=self.primary_color)
        unit_label.grid(row=3, column=0, sticky=tk.W, padx=5, pady=10)
        
        self.unit_var = tk.StringVar()
        self.unit_entry = ttk.Entry(form_frame, textvariable=self.unit_var, width=20, style="Editor.TEntry")
        self.unit_entry.grid(row=3, column=1, sticky=tk.W, padx=5, pady=10)
        
        # 常用单位快速选择
        unit_frame = ttk.Frame(form_frame)
        unit_frame.grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5, pady=10)
        
        unit_label = ttk.Label(unit_frame, text="常用单位:")
        unit_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        common_units = ["斤", "个", "件", "盒", "瓶", "包", "条", "g", "kg", "罐", "板", "块"]
        # 使用网格布局替代水平排列，每行放4个按钮
        row, col = 0, 1  # 从第0行第1列开始（第0列是标签）
        for unit in common_units:
            unit_button = ttk.Button(unit_frame, text=unit, width=5,
                                   command=lambda u=unit: self.unit_var.set(u))
            unit_button.grid(row=row, column=col, padx=2, pady=2)
            col += 1
            # 每行放4个按钮，超过就换行
            if col > 4:
                col = 1
                row += 1
        
        # 配置unit_frame的列权重
        for i in range(5):  # 0-4列
            unit_frame.columnconfigure(i, weight=1)
        
        # 配置网格权重
        form_frame.columnconfigure(1, weight=1)
    
    def _load_items(self):
        """加载物品数据到列表"""
        # 清空现有数据
        for item in self.item_tree.get_children():
            self.item_tree.delete(item)
        
        # 添加物品数据
        for name, info in self.goods_data.items():
            self.item_tree.insert("", tk.END, values=(name, info['id'], info['no'], info['unit']))
        
        # 按名称排序
        self._sort_items()
    
    def _sort_items(self):
        """对物品列表按名称排序"""
        items = [(self.item_tree.set(item, "name"), item) for item in self.item_tree.get_children('')]
        items.sort()
        
        # 重新排序
        for index, (_, item) in enumerate(items):
            self.item_tree.move(item, '', index)
    
    def _filter_items(self, *args):
        """根据搜索条件过滤物品列表"""
        search_text = self.search_var.get().lower()
        
        # 清空现有数据
        for item in self.item_tree.get_children():
            self.item_tree.delete(item)
        
        # 添加匹配的物品数据
        for name, info in self.goods_data.items():
            if (search_text in name.lower() or 
                search_text in info['id'].lower() or 
                search_text in info['no'].lower()):
                self.item_tree.insert("", tk.END, values=(name, info['id'], info['no'], info['unit']))
        
        # 按名称排序
        self._sort_items()
    
    def _on_item_select(self, event):
        """处理物品选择事件"""
        selected_items = self.item_tree.selection()
        if not selected_items:
            self._disable_edit_form()
            return
        
        # 获取选中的物品数据
        item = selected_items[0]
        name = self.item_tree.item(item, "values")[0]
        info = self.goods_data.get(name, {})
        
        # 填充表单
        self.name_var.set(name)
        self.id_var.set(info.get('id', ''))
        self.no_var.set(info.get('no', ''))
        self.unit_var.set(info.get('unit', ''))
        
        # 启用编辑表单
        self._enable_edit_form()
    
    def _add_new_item(self):
        """添加新物品"""
        # 清空并启用表单
        self.name_var.set("")
        # 自动生成新ID和编号
        import uuid
        new_id = str(uuid.uuid4()).replace('-', '')
        new_no = "WP" + str(len(self.goods_data) + 1).zfill(8)
        self.id_var.set(new_id)
        self.no_var.set(new_no)
        self.unit_var.set("")
        
        # 启用编辑表单
        self._enable_edit_form()
        
        # 设置焦点到名称输入框
        self.name_entry.focus()
        
        # 不再自动保存新物品，等待用户输入名称后再保存
    
    def _delete_item(self):
        """删除选中的物品"""
        selected_items = self.item_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的物品")
            return
        
        # 获取选中的物品名称
        item = selected_items[0]
        name = self.item_tree.item(item, "values")[0]
        
        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除物品 '{name}' 吗？"):
            # 从数据中删除
            if name in self.goods_data:
                del self.goods_data[name]
            
            # 从列表中删除
            self.item_tree.delete(item)
            
            # 禁用编辑表单
            self._disable_edit_form()
    
    def _enable_edit_form(self):
        """启用编辑表单"""
        self.name_entry.configure(state="normal")
        self.id_entry.configure(state="normal")
        self.no_entry.configure(state="normal")
        self.unit_entry.configure(state="normal")
    
    def _disable_edit_form(self):
        """禁用编辑表单"""
        self.name_var.set("")
        self.id_var.set("")
        self.no_var.set("")
        self.unit_var.set("")
        
        self.name_entry.configure(state="disabled")
        self.id_entry.configure(state="disabled")
        self.no_entry.configure(state="disabled")
        self.unit_entry.configure(state="disabled")
    
    def save_changes(self):
        """保存所有更改"""
        # 先保存当前编辑的物品
        self._save_current_item()
        
        # 保存到item.json文件
        try:
            # 获取可能的应用程序目录路径
            app_dir = os.path.dirname(os.path.abspath(__file__))
            # 修改保存路径优先级，优先使用可执行文件所在目录
            possible_paths = [
                os.path.dirname(sys.executable),  # 可执行文件所在目录（优先）
                os.path.abspath(os.path.curdir),  # 当前工作目录
                app_dir,  # 当前脚本所在目录
                os.path.dirname(os.path.abspath(sys.argv[0])),  # 执行脚本的目录
                getattr(sys, '_MEIPASS', app_dir)  # PyInstaller打包时的临时目录
            ]
            
            # 添加更多日志输出
            print(f"保存物品数据，当前目录: {os.getcwd()}")
            print(f"可能的保存路径列表: {possible_paths}")
            print(f"物品数据条目数: {len(self.goods_data)}")
            
            # 尝试在所有可能的路径中保存
            saved = False
            save_error = None
            
            # 首先尝试直接在当前工作目录保存
            try:
                item_json_path = 'item.json'  # 直接使用相对路径
                print(f"尝试保存到当前工作目录: {os.path.abspath(item_json_path)}")
                
                with open(item_json_path, 'w', encoding='utf-8') as f:
                    json.dump(self.goods_data, f, ensure_ascii=False, indent=2)
                
                print(f"成功保存到当前工作目录: {os.path.abspath(item_json_path)}")
                saved = True
            except Exception as e:
                save_error = e
                print(f"保存到当前工作目录失败: {str(e)}")
                
                # 如果直接保存失败，再尝试其他路径
                for path in possible_paths:
                    try:
                        # 确保路径存在
                        if not os.path.exists(path):
                            print(f"路径不存在，跳过: {path}")
                            continue
                            
                        # 检查路径是否可写
                        if not os.access(path, os.W_OK):
                            print(f"路径不可写，跳过: {path}")
                            continue
                        
                        item_json_path = os.path.join(path, 'item.json')
                        print(f"尝试保存到: {item_json_path}")
                        
                        # 尝试打开文件
                        with open(item_json_path, 'w', encoding='utf-8') as f:
                            json.dump(self.goods_data, f, ensure_ascii=False, indent=2)
                        
                        print(f"成功保存到: {item_json_path}")
                        saved = True
                        break
                    except Exception as e:
                        save_error = e
                        print(f"保存到 {path} 失败: {str(e)}")
                        continue
            
            if saved:
                messagebox.showinfo("成功", "物品数据已成功保存！")
                
                # 如果有回调函数，通知主窗口重新加载数据
                if self.callback:
                    self.callback()
            else:
                error_msg = f"无法保存物品数据到任何可能的路径：{str(save_error)}"
                print(f"错误: {error_msg}")
                messagebox.showerror("错误", error_msg)
                
                # 尝试保存到用户可访问的位置作为备用方案
                try:
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    backup_path = os.path.join(desktop_path, "item_backup.json")
                    print(f"尝试保存备份到桌面: {backup_path}")
                    
                    with open(backup_path, 'w', encoding='utf-8') as f:
                        json.dump(self.goods_data, f, ensure_ascii=False, indent=2)
                        
                    messagebox.showinfo("备份已保存", f"已将数据备份保存到桌面: {backup_path}")
                except Exception as backup_error:
                    print(f"备份保存失败: {str(backup_error)}")
                
        except Exception as e:
            error_msg = f"保存物品数据时出错：{str(e)}"
            print(f"异常: {error_msg}")
            messagebox.showerror("错误", error_msg)
    
    def _save_current_item(self):
        """保存当前编辑的物品"""
        name = self.name_var.get().strip()
        item_id = self.id_var.get().strip()
        item_no = self.no_var.get().strip()
        unit = self.unit_var.get().strip()
        
        # 验证数据
        if name and (item_id or item_no):
            # 检查是否是重命名
            selected_items = self.item_tree.selection()
            if selected_items:
                old_name = self.item_tree.item(selected_items[0], "values")[0]
                
                # 如果名称已更改，需要删除旧名称的数据
                if old_name != name and old_name in self.goods_data:
                    del self.goods_data[old_name]
            
            # 更新数据
            self.goods_data[name] = {
                'id': item_id,
                'no': item_no,
                'unit': unit
            }
            
            # 刷新列表
            self._load_items()
    
    def _on_close(self):
        """窗口关闭事件处理"""
        # 检查是否有未保存的更改
        name = self.name_var.get().strip()
        item_id = self.id_var.get().strip()
        item_no = self.no_var.get().strip()
        
        if name and (item_id or item_no):
            if messagebox.askyesno("未保存的更改", "有未保存的更改，是否保存？"):
                self.save_changes()
        
        self.window.destroy()

# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    editor = ItemEditor(root)
    root.mainloop()