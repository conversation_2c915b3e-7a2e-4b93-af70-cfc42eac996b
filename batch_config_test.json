{"default_config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e61", "entity.operatorUser": "汕尾城区护理院", "entity.manuf.id": "2c901acf942ba24f01942bb69a9b1e5a", "entity.manufLinkTellp": "110", "entity.manufAddress1": "测试地址"}, "tables": [{"name": "示例表格1", "description": "这是一个示例表格", "config": {"entity.intoDate": "2025-01-20 10:00:00", "entity.deliveryNnoteNo": "DN001", "entity.notes": "示例入库"}, "goods": [{"name": "瘦肉", "quantity": "5", "price": "20", "unit": "g", "batchNo": "B001", "productionDate": "2025-01-18", "expireDate": "2025-01-25", "notes": "新鲜瘦肉"}, {"name": "莲蓉包", "quantity": "10", "price": "2", "unit": "个", "batchNo": "B002", "productionDate": "2025-01-19", "expireDate": "2025-01-22", "notes": "早餐用"}]}, {"name": "1", "description": "", "config": {"entity.intoDate": "2025-06-06 12:05:53", "entity.deliveryNnoteNo": "", "entity.notes": ""}, "goods": [{"name": "莲蓉包", "quantity": 1, "price": 1, "unit": "个", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "莲蓉包", "quantity": 20, "price": 2, "unit": "个", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}]}, {"name": "测试表格_增强配置", "description": "测试入库类型、仓库ID、操作员配置", "config": {"entity.intoKey.id": "STOCKADVANCE_IN_RETURN", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e62", "entity.operatorUser": "管理员", "entity.intoDate": "2025-01-20 15:30:00", "entity.deliveryNnoteNo": "TEST001", "entity.notes": "测试增强配置功能"}, "goods": [{"name": "测试商品", "quantity": "1", "price": "10", "unit": "个", "batchNo": "TEST001", "productionDate": "2025-01-20", "expireDate": "2025-01-25", "notes": "测试商品"}]}], "predefined_options": {"entity.intoKey.id": {"STOCKADVANCE_IN_PURCHASE": "采购入库", "STOCKADVANCE_IN_RETURN": "退货入库", "STOCKADVANCE_IN_TRANSFER": "调拨入库"}, "entity.storeroom.id": {"2c901acf942ba24f01942bb7166c1e61": "主仓库", "2c901acf942ba24f01942bb7166c1e62": "副仓库"}, "entity.operatorUser": {"汕尾城区护理院": "汕尾城区护理院", "管理员": "管理员"}}}