#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用的Excel解析逻辑（不依赖商品名称过滤）
"""

def parse_goods_from_lines_universal(lines, warehouse_note, is_left_table=True):
    """通用的商品解析函数（不依赖商品名称过滤）"""
    goods = []

    # 查找数据开始行（包含"名称及规格"的下一行）
    data_start = -1
    for i, line in enumerate(lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            break

    if data_start == -1:
        return goods

    # 首先分析所有数据行，确定哪些行有左侧数据，哪些行有右侧数据
    data_lines = []
    for line in lines[data_start:]:
        line = line.strip()
        if not line or '合计金额' in line or '送货人' in line:
            break
        data_lines.append(line)

    # 分析每行的结构
    analyzed_lines = []
    for line in data_lines:
        parts = line.split('\t')
        line_info = {
            'parts': parts,
            'has_left': False,
            'has_right': False,
            'left_data': None,
            'right_data': None
        }
        
        if len(parts) >= 12:
            # 完整行：检查左侧和右侧是否有数据
            left_name = parts[0].strip()
            left_quantity = parts[2].strip()
            right_name = parts[7].strip()
            right_quantity = parts[9].strip()
            
            if left_name and left_quantity:
                line_info['has_left'] = True
                line_info['left_data'] = {
                    'name': left_name,
                    'unit': parts[1].strip(),
                    'quantity': left_quantity,
                    'price': parts[3].strip()
                }
            
            if right_name and right_quantity:
                line_info['has_right'] = True
                line_info['right_data'] = {
                    'name': right_name,
                    'unit': parts[8].strip(),
                    'quantity': right_quantity,
                    'price': parts[10].strip()
                }
        
        elif len(parts) >= 5:
            # 简化行：这些数据属于右侧表格（因为左侧为空时Excel会省略制表符）
            name = parts[0].strip()
            quantity = parts[2].strip()
            
            if name and quantity:
                line_info['has_right'] = True
                line_info['right_data'] = {
                    'name': name,
                    'unit': parts[1].strip(),
                    'quantity': quantity,
                    'price': parts[3].strip()
                }
        
        analyzed_lines.append(line_info)

    # 根据分析结果提取对应表格的数据
    for line_info in analyzed_lines:
        data_to_process = None
        
        if is_left_table and line_info['has_left']:
            data_to_process = line_info['left_data']
        elif not is_left_table and line_info['has_right']:
            data_to_process = line_info['right_data']
        
        if data_to_process:
            name = data_to_process['name']
            unit = data_to_process['unit']
            quantity = data_to_process['quantity']
            price = data_to_process['price']
            
            # 验证数量和价格是否为数字，并且数量不为0
            try:
                quantity_float = float(quantity)
                price_float = float(price)
                if quantity_float <= 0:  # 跳过数量为0或负数的商品
                    continue
            except ValueError:
                continue

            goods.append({
                'name': name,
                'quantity': quantity,
                'price': price,
                'unit': unit,
                'batchNo': '',
                'productionDate': '',
                'expireDate': '',
                'notes': ''
            })

    return goods

def test_universal_parsing():
    """测试通用的解析逻辑"""
    print("=== 测试通用的Excel解析逻辑 ===")
    
    # 读取实例文件
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取员工表格部分（第25-50行）
    employee_lines = []
    for i in range(25, 50):
        if i < len(lines):
            employee_lines.append(lines[i].rstrip('\n'))
    
    print("分析数据结构:")
    
    # 查找数据开始行
    data_start = -1
    for i, line in enumerate(employee_lines):
        if '名称及规格' in line and '数量' in line and '单价' in line:
            data_start = i + 1
            break
    
    if data_start != -1:
        data_lines = []
        for line in employee_lines[data_start:]:
            line = line.strip()
            if not line or '合计金额' in line or '送货人' in line:
                break
            data_lines.append(line)
        
        print(f"找到 {len(data_lines)} 行数据")
        
        # 分析每行结构
        for i, line in enumerate(data_lines):
            parts = line.split('\t')
            left_name = ""
            right_name = ""
            
            if len(parts) >= 12:
                left_name = parts[0].strip()
                right_name = parts[7].strip()
                print(f"行{i+1}: {len(parts)}列 - 左侧: '{left_name}', 右侧: '{right_name}'")
            elif len(parts) >= 5:
                name = parts[0].strip()
                print(f"行{i+1}: {len(parts)}列 - 简化行: '{name}' (归属右侧)")
            else:
                print(f"行{i+1}: {len(parts)}列 - 无效行")
    
    # 解析左侧表格（员工）
    print("\n解析员工表格:")
    employee_goods = parse_goods_from_lines_universal(employee_lines, "员工", is_left_table=True)
    
    print(f"员工表格解析结果（{len(employee_goods)}个商品）:")
    for goods in employee_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']} × {goods['price']}元")
    
    # 解析右侧表格（城区特困长者）
    print("\n解析城区特困长者表格:")
    city_elderly_goods = parse_goods_from_lines_universal(employee_lines, "城区特困长者", is_left_table=False)
    
    print(f"城区特困长者表格解析结果（{len(city_elderly_goods)}个商品）:")
    for goods in city_elderly_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']} × {goods['price']}元")
    
    # 验证结果
    employee_names = [goods['name'] for goods in employee_goods]
    city_elderly_names = [goods['name'] for goods in city_elderly_goods]
    
    print(f"\n验证结果:")
    print(f"员工商品: {employee_names}")
    print(f"城区特困长者商品: {city_elderly_names}")
    
    # 预期结果
    expected_employee_items = ["肚肉", "瘦肉", "光鸡整只去爪", "包菜", "胡萝卜", "白菜", "芹葱蒜", "广霸米粉", "黄小米"]
    expected_city_elderly_items = [
        "肚肉", "瘦肉", "排骨", "豆干", "光鸡整只去爪", "包菜", "胡萝卜", "白菜", 
        "苦瓜", "芹葱蒜", "广霸米粉", "绿豆", "黄小米", "白糖", "热狗"
    ]
    
    # 验证员工表格
    employee_correct = set(employee_names) == set(expected_employee_items)
    city_elderly_correct = set(city_elderly_names) == set(expected_city_elderly_items)
    
    print(f"\n详细验证:")
    if employee_correct:
        print(f"✅ 员工表格解析完全正确")
    else:
        missing = set(expected_employee_items) - set(employee_names)
        extra = set(employee_names) - set(expected_employee_items)
        print(f"❌ 员工表格解析有误，缺少: {missing}, 多余: {extra}")
    
    if city_elderly_correct:
        print(f"✅ 城区特困长者表格解析完全正确")
    else:
        missing = set(expected_city_elderly_items) - set(city_elderly_names)
        extra = set(city_elderly_names) - set(expected_city_elderly_items)
        print(f"❌ 城区特困长者表格解析有误，缺少: {missing}, 多余: {extra}")
    
    return employee_correct, city_elderly_correct

def main():
    """主测试函数"""
    print("开始测试通用的Excel解析逻辑（不依赖商品名称过滤）...\n")
    
    employee_correct, city_elderly_correct = test_universal_parsing()
    
    print(f"\n=== 最终测试结果 ===")
    print(f"员工表格解析: {'✅ 通过' if employee_correct else '❌ 失败'}")
    print(f"城区特困长者表格解析: {'✅ 通过' if city_elderly_correct else '❌ 失败'}")
    
    if employee_correct and city_elderly_correct:
        print("\n🎉 通用Excel解析逻辑测试成功！")
        print("✅ 不依赖商品名称过滤，通用性更强")
        print("✅ 基于数据结构分析，更加可靠")
        print("✅ 适用于任何商品名称和数量的表格")
    else:
        print("\n❌ 还有问题需要进一步修复。")

if __name__ == "__main__":
    main()
