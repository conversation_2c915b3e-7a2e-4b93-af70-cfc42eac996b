import pandas as pd

# 创建商品数据 - 根据test.json更新字段
goods_data = {
    '商品ID': ['2c901acf942ba24f01942bd510cf2081', '2c901acf943b11cc01944874f49f0cb8', '2c901acf943b11cc01944878aec20ce9', '2c901acf943b11cc0194487947150cff'],
    '商品编号': ['WP00000004', 'WP00000005', 'WP00000006', 'WP00000007'],
    '商品名称': ['瘦肉', '莲蓉包', '猪头皮', '猪头骨'],
    '批号': ['888', '5387', '', ''],
    '生产日期': ['2025-03-18', '2025-03-13', '', ''],
    '生产厂家': ['', '', '', ''],
    '有效期': ['2025-03-13', '2025-03-11', '', ''],
    '数量': ['5', '6', '1', '1'],
    '单位': ['g', '个', '斤', '斤'],
    '单价': ['20', '2', '0', '0'],
    '小计': ['100', '12', '0', '0'],
    '分拆单位': ['', '', '', ''],
    '分拆数量': ['', '', '', ''],
    '备注': ['通分', '', '', '']
}

# 创建其他信息数据 - 根据test.json更新字段
other_data = {
    '入库单号': [''],
    '入库日期': ['2025-03-01 19:06:59'],
    '入库类型ID': ['STOCKADVANCE_IN_PURCHASE'],
    '仓库ID': ['2c901acf942ba24f01942bb7166c1e61'],
    '操作员': ['汕尾城区护理院'],
    '供应商ID': ['2c901acf942ba24f01942bb69a9b1e5a'],
    '送货单号': ['21'],
    '供应商联系电话': ['110'],
    '供应商地址': ['测试地址'],
    '总金额': ['112'],
    '实际金额': ['112'],
    '备注': ['入库备注']
}

# 创建DataFrame
goods_df = pd.DataFrame(goods_data)
other_df = pd.DataFrame(other_data)

# 添加数据格式说明
format_notes = """
数据字段说明：

第一个工作表（基本信息）：
1. 入库单号：选填，唯一标识符
2. 入库日期：选填，格式YYYY-MM-DDTHH:MM:SS，如果留空则会自动转换为当前系统时间
3. 入库类型ID：必填，默认为STOCKADVANCE_IN_PURCHASE
4. 仓库ID：必填，默认为2c901acf942ba24f01942bb7166c1e61
5. 操作员：必填，默认为汕尾城区护理院
6. 供应商ID：必填
7. 送货单号：选填
8. 供应商联系电话：选填
9. 供应商地址：选填
10. 总金额：必填，精确到小数点后2位
11. 实际金额：必填，精确到小数点后2位
12. 备注：选填

第二个工作表（商品明细）：
1. 商品ID：选填，唯一标识符，如果留空会从item.json中获取相应值
2. 商品编号：选填，系统编号，如果留空会从item.json中获取相应值
3. 商品名称：选填，商品名称，如果留空会从item.json中获取相应值
4. 批号：选填，生产批号
5. 生产日期：选填，格式YYYY-MM-DD
6. 生产厂家：选填，生产商名称
7. 有效期：选填，格式YYYY-MM-DD
8. 数量：必填，数量必须为正整数
9. 单位：必填，商品计量单位
10. 单价：必填，精确到小数点后2位
11. 小计：必填，数量*单价
12. 分拆单位：选填
13. 分拆数量：选填
14. 备注：选填，商品相关备注信息
注：如果商品名称、商品编号、商品ID在item.json没有相应值，则必填
"""

# 将数据写入Excel的不同工作表
with pd.ExcelWriter('template.xlsx', engine='openpyxl') as writer:
    other_df.to_excel(writer, sheet_name='基本信息', index=False)
    goods_df.to_excel(writer, sheet_name='商品明细', index=False)
    pd.DataFrame({'格式说明': [format_notes]}).to_excel(
        writer, 
        sheet_name='填写说明',
        index=False
    )

print("示例模板已创建，包含基本信息和商品明细两个工作表，以及填写说明")