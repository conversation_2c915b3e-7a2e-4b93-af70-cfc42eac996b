{"default_config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e61", "entity.operatorUser": "汕尾城区护理院", "entity.manuf.id": "2c901acf942ba24f01942bb69a9b1e5a", "entity.manufLinkTellp": "110", "entity.manufAddress1": "测试地址"}, "tables": [{"name": "非城区特困长者 2025.06.10", "description": "批量导入 - 非城区特困长者", "config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e63", "entity.operatorUser": "汕尾城区护理院", "entity.intoDate": "2025-06-10 00:00:00", "entity.deliveryNnoteNo": "", "entity.notes": "批量导入 - 非城区特困长者"}, "goods": [{"name": "肚肉", "quantity": "6", "price": "15", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "瘦肉", "quantity": "8", "price": "19", "unit": "g", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "排骨", "quantity": "1.6", "price": "27", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "豆干", "quantity": "45", "price": "2.5", "unit": "块", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "光鸡整只去爪", "quantity": "23.5", "price": "12.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "包菜", "quantity": "9.8", "price": "2.4", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "胡萝卜", "quantity": "5", "price": "3", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白菜", "quantity": "20", "price": "3.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "苦瓜", "quantity": "4", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "芹葱蒜", "quantity": "2", "price": "7", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "广霸米粉", "quantity": "11.25", "price": "4", "unit": "条", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "绿豆", "quantity": "5", "price": "8", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "黄小米", "quantity": "4", "price": "5.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白糖", "quantity": "5", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "热狗", "quantity": "4", "price": "10", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}]}, {"name": "员工 2025.06.10", "description": "批量导入 - 员工", "config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e64", "entity.operatorUser": "汕尾城区护理院", "entity.intoDate": "2025-06-10 00:00:00", "entity.deliveryNnoteNo": "", "entity.notes": "批量导入 - 员工"}, "goods": [{"name": "肚肉", "quantity": "1.1", "price": "15", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "瘦肉", "quantity": "1.4", "price": "19", "unit": "g", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "排骨", "quantity": "1.3", "price": "27", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "豆干", "quantity": "35.0", "price": "2.5", "unit": "块", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "光鸡整只去爪", "quantity": "3.5", "price": "12.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "包菜", "quantity": "2.1", "price": "2.4", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "胡萝卜", "quantity": "0.5", "price": "3", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白菜", "quantity": "4.3", "price": "3.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "苦瓜", "quantity": "3.2", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "芹葱蒜", "quantity": "0.2", "price": "7", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "广霸米粉", "quantity": "2.45", "price": "4", "unit": "条", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "绿豆", "quantity": "3.9", "price": "8", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "黄小米", "quantity": "0.5", "price": "5.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白糖", "quantity": "3.9", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "热狗", "quantity": "3.3", "price": "10", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}]}, {"name": "城区特困长者 2025.06.10", "description": "批量导入 - 城区特困长者", "config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e65", "entity.operatorUser": "汕尾城区护理院", "entity.intoDate": "2025-06-10 00:00:00", "entity.deliveryNnoteNo": "", "entity.notes": "批量导入 - 城区特困长者"}, "goods": [{"name": "肚肉", "quantity": "3.7", "price": "15", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "瘦肉", "quantity": "5.0", "price": "19", "unit": "g", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "光鸡整只去爪", "quantity": "15.5", "price": "12.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "包菜", "quantity": "6.0", "price": "2.4", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "胡萝卜", "quantity": "3.6", "price": "3", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白菜", "quantity": "12.1", "price": "3.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "芹葱蒜", "quantity": "1.4", "price": "7", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "广霸米粉", "quantity": "6.8", "price": "4", "unit": "条", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "黄小米", "quantity": "2.8", "price": "5.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}]}, {"name": "自费长者 2025.06.10", "description": "批量导入 - 自费长者", "config": {"entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE", "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e66", "entity.operatorUser": "汕尾城区护理院", "entity.intoDate": "2025-06-10 00:00:00", "entity.deliveryNnoteNo": "", "entity.notes": "批量导入 - 自费长者"}, "goods": [{"name": "肚肉", "quantity": "0.4", "price": "15", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "瘦肉", "quantity": "0.5", "price": "19", "unit": "g", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "排骨", "quantity": "0.1", "price": "27", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "豆干", "quantity": "3.0", "price": "2.5", "unit": "块", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "光鸡整只去爪", "quantity": "1.4", "price": "12.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "包菜", "quantity": "0.5", "price": "2.4", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "胡萝卜", "quantity": "0.3", "price": "3", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白菜", "quantity": "1.1", "price": "3.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "苦瓜", "quantity": "0.2", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "芹葱蒜", "quantity": "0.1", "price": "7", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "广霸米粉", "quantity": "0.6", "price": "4", "unit": "条", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "绿豆", "quantity": "0.3", "price": "8", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "黄小米", "quantity": "0.2", "price": "5.5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "白糖", "quantity": "0.3", "price": "5", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}, {"name": "热狗", "quantity": "0.2", "price": "10", "unit": "斤", "batchNo": "", "productionDate": "", "expireDate": "", "notes": ""}]}], "predefined_options": {"entity.intoKey.id": {"STOCKADVANCE_IN_PURCHASE": "采购入库", "STOCKADVANCE_IN_RETURN": "退货入库", "STOCKADVANCE_IN_TRANSFER": "调拨入库"}, "entity.storeroom.id": {"2c901acf942ba24f01942bb7166c1e61": "主仓库", "2c901acf942ba24f01942bb7166c1e62": "副仓库", "2c901acf942ba24f01942bb7166c1e63": "非城区特困长者仓库", "2c901acf942ba24f01942bb7166c1e64": "员工仓库", "2c901acf942ba24f01942bb7166c1e65": "城区特困长者仓库", "2c901acf942ba24f01942bb7166c1e66": "自费长者仓库"}, "entity.operatorUser": {"汕尾城区护理院": "汕尾城区护理院", "管理员": "管理员"}}}