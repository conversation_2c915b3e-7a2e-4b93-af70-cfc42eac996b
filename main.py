import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import pandas as pd
import json
import os
import sys
from datetime import datetime
from collections import OrderedDict
from goods_mapping import GoodsMapper
from item_editor import ItemEditor
from item_matcher import ItemMatcher
from batch_converter import BatchConverter

def get_app_directory():
    """获取应用程序所在目录，兼容打包后的情况"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 如果是开发环境
        return os.path.dirname(os.path.abspath(__file__))

class ExcelToJsonConverter:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("Excel转JSON工具")
        self.window.geometry("800x600")
        
        # 设置中文字体和主题样式
        self.window.option_add("*Font", "微软雅黑 10")
        
        # 图标已通过PyInstaller的icon参数嵌入到exe中，无需手动设置
        
        # 创建样式对象
        style = ttk.Style()
        style.theme_use('clam')
        
        # 定义颜色方案 - 更现代的配色
        primary_color = "#3f51b5"  # 主色调：靛蓝色
        secondary_color = "#f5f7fa"  # 次要色调：浅灰蓝色
        accent_color = "#7986cb"  # 强调色：淡紫蓝色
        text_color = "#2c3e50"  # 文本色：深蓝灰色
        success_color = "#4caf50"  # 成功色：绿色
        error_color = "#f44336"  # 错误色：红色
        warning_color = "#ff9800"  # 警告色：橙色
        info_color = "#2196f3"  # 信息色：蓝色
        border_radius = 8  # 边框圆角半径
        
        # 自定义按钮样式 - 添加圆角和阴影效果
        style.configure('Custom.TButton',
                       background=primary_color,
                       foreground="white",
                       padding="8 12",
                       font=('微软雅黑', 10, 'bold'),
                       relief="flat",
                       borderwidth=0)
        style.map('Custom.TButton',
                 background=[('active', accent_color), ('pressed', primary_color)],
                 foreground=[('active', 'white'), ('pressed', 'white')])
                 
        # 创建悬停效果按钮样式
        style.configure('Hover.TButton',
                       background=accent_color,
                       foreground="white",
                       padding="8 12",
                       font=('微软雅黑', 10, 'bold'),
                       relief="flat",
                       borderwidth=0)
        style.map('Hover.TButton',
                 background=[('active', primary_color), ('pressed', primary_color)],
                 foreground=[('active', 'white'), ('pressed', 'white')])
        
        # 自定义标签框样式 - 添加圆角和阴影效果
        style.configure('TLabelframe', 
                       background=secondary_color,
                       bordercolor=accent_color,
                       borderwidth=1,
                       relief="solid")
        style.configure('TLabelframe.Label', 
                       font=('微软雅黑', 10, 'bold'),
                       foreground=primary_color,
                       background=secondary_color,
                       padding="5 2")
        
        # 自定义输入框样式 - 添加圆角和边框效果
        style.configure('TEntry', 
                       fieldbackground="white",
                       bordercolor=accent_color,
                       borderwidth=1,
                       relief="solid",
                       padding=5)
        
        # 自定义进度条样式 - 添加圆角和流畅效果
        style.configure("TProgressbar", 
                       troughcolor=secondary_color,
                       background=info_color,
                       bordercolor=accent_color,
                       lightcolor=info_color,
                       darkcolor=primary_color,
                       thickness=15,
                       borderwidth=0)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.window, padding="20 20 20 0")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建工具栏框架
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 添加版本信息
        version_label = ttk.Label(self.toolbar_frame, text="v1.0.0")
        version_label.pack(side=tk.RIGHT)
        
        # 文件选择区域
        self.files_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding=10)
        self.files_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Excel文件选择
        excel_label = ttk.Label(self.files_frame, text="Excel文件:")
        excel_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        self.excel_path = tk.StringVar()
        self.excel_entry = ttk.Entry(self.files_frame, textvariable=self.excel_path, width=60)
        self.excel_entry.grid(row=0, column=1, padx=(0, 5), sticky=tk.EW)
        
        self.excel_button = ttk.Button(self.files_frame, text="浏览",
                                     command=self.select_excel, style='Custom.TButton')
        self.excel_button.grid(row=0, column=2)
        
        # 预览按钮
        self.preview_button = ttk.Button(self.files_frame, text="预览数据",
                                       command=self.preview_data, style='Custom.TButton')
        self.preview_button.grid(row=0, column=3, padx=(5, 0))
        
        # JSON文件保存位置
        json_label = ttk.Label(self.files_frame, text="保存位置:")
        json_label.grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        
        self.json_path = tk.StringVar()
        self.json_entry = ttk.Entry(self.files_frame, textvariable=self.json_path, width=60)
        self.json_entry.grid(row=1, column=1, padx=(0, 5), pady=(10, 0), sticky=tk.EW)
        
        self.json_button = ttk.Button(self.files_frame, text="浏览",
                                    command=self.select_save_path, style='Custom.TButton')
        self.json_button.grid(row=1, column=2, pady=(10, 0))
        
        # 配置网格列的权重
        self.files_frame.columnconfigure(1, weight=1)
        
        # 操作按钮区域
        self.buttons_frame = ttk.Frame(self.main_frame)
        self.buttons_frame.pack(fill=tk.X, pady=10)
        
        # 转换按钮
        self.convert_button = ttk.Button(self.buttons_frame, text="开始转换", 
                                       command=self.convert, style='Custom.TButton', width=15)
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清除日志按钮
        self.clear_log_button = ttk.Button(self.buttons_frame, text="清除日志",
                                          command=self.clear_log, style='Custom.TButton')
        self.clear_log_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看示例模板按钮
        self.template_button = ttk.Button(self.buttons_frame, text="查看示例模板",
                                        command=self.open_template, style='Custom.TButton')
        self.template_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 编辑物品按钮
        self.edit_items_button = ttk.Button(self.buttons_frame, text="编辑物品",
                                          command=self.open_item_editor, style='Custom.TButton')
        self.edit_items_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 批量转换按钮
        self.batch_convert_button = ttk.Button(self.buttons_frame, text="批量转换",
                                             command=self.open_batch_converter, style='Custom.TButton')
        self.batch_convert_button.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.main_frame, 
                                          variable=self.progress_var, 
                                          maximum=100,
                                          mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # 日志框
        self.log_frame = ttk.LabelFrame(self.main_frame, text="转换日志", padding=10)
        self.log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志文本框和滚动条
        log_scroll = ttk.Scrollbar(self.log_frame, style="Log.Vertical.TScrollbar")
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置日志文本框样式
        self.log_text = tk.Text(self.log_frame, height=10, 
                               yscrollcommand=log_scroll.set,
                               wrap=tk.WORD,
                               font=("微软雅黑", 9),
                               bg="#fafafa",
                               fg=text_color,
                               padx=5,
                               pady=5,
                               relief="flat",
                               borderwidth=1)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # 设置日志滚动条样式
        style.configure("Log.Vertical.TScrollbar", 
                        background=secondary_color, 
                        troughcolor="white", 
                        arrowcolor=primary_color)
        
        # 配置标签
        self.log_text.tag_configure("error", foreground="#e74c3c")
        self.log_text.tag_configure("success", foreground="#2ecc71")
        self.log_text.tag_configure("info", foreground="#3498db")
        self.log_text.tag_configure("warning", foreground="#f39c12")
        
        log_scroll.config(command=self.log_text.yview)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_bar = ttk.Label(self.window, 
                                  textvariable=self.status_var, 
                                  relief=tk.FLAT, 
                                  background=primary_color,
                                  foreground="white",
                                  font=("微软雅黑", 9),
                                  padding=(10, 5))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 存储转换过程中的数据，用于重新转换
        self.last_excel_path = None
        self.last_json_path = None
        self.last_goods_df = None
        self.last_other_df = None
        self.last_field_mapping = None
        self.last_goods_mapper = None
        self.current_json_window = None  # 当前的JSON预览窗口
            
    def select_excel(self):
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx;*.xls")]
        )
        if filename:
            self.excel_path.set(filename)
            # 自动设置JSON保存路径
            json_path = os.path.splitext(filename)[0] + ".json"
            self.json_path.set(json_path)
            
    def select_save_path(self):
        filename = filedialog.asksaveasfilename(
            title="选择保存位置",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json")]
        )
        if filename:
            self.json_path.set(filename)
            
    def clear_log(self):
        self.log_text.delete(1.0, tk.END)
        self.status_var.set("日志已清除")
        
    def preview_data(self):
        try:
            excel_path = self.excel_path.get()
            if not excel_path:
                messagebox.showerror("错误", "请先选择Excel文件")
                return
                
            # 创建预览窗口
            preview_window = tk.Toplevel(self.window)
            preview_window.title("数据预览")
            preview_window.geometry("800x600")
            
            # 创建选项卡控件
            notebook = ttk.Notebook(preview_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            try:
                # 尝试读取基本信息工作表
                basic_df = pd.read_excel(excel_path, sheet_name='基本信息')
                basic_frame = ttk.Frame(notebook)
                notebook.add(basic_frame, text='基本信息')
                self._create_data_table(basic_frame, basic_df)
            except Exception as e:
                self.log(f"读取基本信息工作表失败: {str(e)}")
            
            try:
                # 尝试读取商品明细工作表
                goods_df = pd.read_excel(excel_path, sheet_name='商品明细')
                goods_frame = ttk.Frame(notebook)
                notebook.add(goods_frame, text='商品明细')
                self._create_data_table(goods_frame, goods_df)
            except Exception as e:
                self.log(f"读取商品明细工作表失败: {str(e)}")
            
            # 如果没有成功加载任何工作表，尝试读取默认工作表
            if notebook.index("end") == 0:
                try:
                    df = pd.read_excel(excel_path)
                    default_frame = ttk.Frame(notebook)
                    notebook.add(default_frame, text='工作表1')
                    self._create_data_table(default_frame, df)
                except Exception as e:
                    messagebox.showerror("错误", f"无法读取Excel文件: {str(e)}")
            
        except Exception as e:
            messagebox.showerror("错误", f"预览数据时出现错误：{str(e)}")
    
    def _create_data_table(self, parent_frame, df):
        """创建数据表格"""
        # 获取样式对象
        style = ttk.Style()
        
        # 定义颜色方案 - 与__init__方法中相同
        primary_color = "#3f51b5"  # 主色调：靛蓝色
        secondary_color = "#f5f7fa"  # 次要色调：浅灰蓝色
        accent_color = "#7986cb"  # 强调色：淡紫蓝色
        text_color = "#2c3e50"  # 文本色：深蓝灰色
        
        # 创建表格框架
        table_frame = ttk.Frame(parent_frame, style="Table.TFrame")
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        style.configure("Table.TFrame", background="white")
        
        # 创建表格
        columns = list(df.columns)
        tree = ttk.Treeview(table_frame, columns=columns, show="headings", style="Preview.Treeview")
        
        # 设置表格样式
        style.configure("Preview.Treeview",
                       background="white",
                       foreground=text_color,
                       rowheight=25,
                       fieldbackground="white",
                       borderwidth=0,
                       font=("微软雅黑", 9))
        style.configure("Preview.Treeview.Heading",
                       background=primary_color,
                       foreground="white",
                       relief="flat",
                       font=("微软雅黑", 10, "bold"))
        style.map("Preview.Treeview",
                 background=[('selected', accent_color)],
                 foreground=[('selected', 'white')])
        
        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            # 根据内容设置列宽
            max_width = len(str(col)) * 10 + 20
            for i, row in df.iterrows():
                if i > 100:  # 只检查前100行以提高性能
                    break
                cell_width = len(str(row[col])) * 8 + 20
                max_width = max(max_width, min(cell_width, 300))  # 限制最大宽度为300
            tree.column(col, width=max_width, minwidth=50)
        
        # 添加数据行
        for i, row in df.iterrows():
            # 交替行颜色
            tag = 'even' if i % 2 == 0 else 'odd'
            tree.insert("", tk.END, values=list(row), tags=(tag,))
        
        # 设置交替行颜色
        tree.tag_configure('odd', background="#f5f5f5")
        tree.tag_configure('even', background="white")
        
        # 添加滚动条
        yscroll = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview, style="Preview.Vertical.TScrollbar")
        xscroll = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=tree.xview, style="Preview.Horizontal.TScrollbar")
        tree.configure(yscrollcommand=yscroll.set, xscrollcommand=xscroll.set)
        
        # 设置滚动条样式
        style.configure("Preview.Vertical.TScrollbar", 
                       background=secondary_color, 
                       troughcolor="white", 
                       arrowcolor=primary_color)
        style.configure("Preview.Horizontal.TScrollbar", 
                       background=secondary_color, 
                       troughcolor="white", 
                       arrowcolor=primary_color)
        
        # 布局
        tree.grid(row=0, column=0, sticky="nsew")
        yscroll.grid(row=0, column=1, sticky="ns")
        xscroll.grid(row=1, column=0, sticky="ew")
        
        # 配置网格权重
        table_frame.grid_columnconfigure(0, weight=1)
        table_frame.grid_rowconfigure(0, weight=1)
            
    def log(self, message, msg_type="info"):
        # 根据消息类型选择标签
        if msg_type not in ["info", "error", "success", "warning"]:
            msg_type = "info"
            
        self.log_text.insert(tk.END, message + "\n", msg_type)
        self.log_text.see(tk.END)
        self.status_var.set(message)
        self.window.update()
        
    def open_template(self):
        template_path = os.path.join(get_app_directory(), 'template.xlsx')
        if os.path.exists(template_path):
            try:
                os.startfile(template_path)
                self.log("已打开示例模板文件", "success")
            except Exception as e:
                messagebox.showerror("错误", f"打开示例模板时出现错误：{str(e)}")
        else:
            messagebox.showerror("错误", "示例模板文件不存在")
            
    def convert(self):
        try:
            # 重置进度条和按钮状态
            self.progress_var.set(0)
            self.status_var.set("开始转换...")
            self.convert_button.configure(state='disabled')
            self.window.update()
            
            # 初始化商品映射器
            self.log("正在加载商品映射关系...", "info")
            goods_mapper = GoodsMapper()
            
            excel_path = self.excel_path.get()
            json_path = self.json_path.get()
            
            if not excel_path or not json_path:
                messagebox.showerror("错误", "请选择Excel文件和保存位置")
                self.convert_button.configure(state='normal')
                return
            
            # 存储转换参数，用于重新转换
            self.last_excel_path = excel_path
            self.last_json_path = json_path
            self.last_goods_mapper = goods_mapper
                
            self.log(f"正在读取Excel文件的基本信息: {excel_path}")
            # 读取基本信息工作表
            try:
                other_df = pd.read_excel(excel_path, sheet_name='基本信息')
                self.progress_var.set(10)
            except Exception as e:
                self.log(f"读取基本信息工作表失败: {str(e)}，尝试读取默认工作表", "error")
                other_df = pd.read_excel(excel_path)
                self.progress_var.set(10)

            self.log("正在读取Excel文件的商品明细...")
            # 读取商品明细工作表
            try:
                goods_df = pd.read_excel(excel_path, sheet_name='商品明细')
                self.progress_var.set(20)
            except Exception as e:
                self.log(f"读取商品明细工作表失败: {str(e)}，将使用基本信息工作表的数据", "error")
                goods_df = pd.DataFrame()
                self.progress_var.set(20)
            
            self.log("正在处理数据...", "info")
            
            # 加载字段映射配置
            try:
                # 获取可能的应用程序目录路径
                app_dir = get_app_directory()
                possible_paths = [
                    app_dir,  # 应用程序所在目录
                    os.path.abspath(os.path.curdir),  # 当前工作目录
                    getattr(sys, '_MEIPASS', app_dir)  # PyInstaller打包时的临时目录（用于资源文件）
                ]
                
                # 尝试从多个可能的路径加载mapping.json
                mapping_config = None
                for path in possible_paths:
                    try:
                        mapping_json_path = os.path.join(path, 'mapping.json')
                        self.log(f"尝试加载字段映射配置，路径: {mapping_json_path}", "info")
                        if os.path.exists(mapping_json_path):
                            with open(mapping_json_path, 'r', encoding='utf-8') as f:
                                mapping_config = json.load(f)
                                field_mapping = mapping_config['excel_to_json_mapping']
                                self.log(f"已成功加载字段映射配置，路径: {mapping_json_path}", "success")
                                break
                    except Exception as e:
                        self.log(f"从路径 {path} 加载mapping.json时出错: {str(e)}", "error")
                
                if mapping_config is None:
                    raise Exception("无法从任何路径加载mapping.json配置文件")
            except Exception as e:
                raise Exception(f"读取mapping.json配置文件失败: {str(e)}")
            
            # 存储数据用于重新转换
            self.last_goods_df = goods_df
            self.last_other_df = other_df
            self.last_field_mapping = field_mapping

            # 使用OrderedDict来保持字段顺序
            data = OrderedDict()
            
            # 处理基本信息
            if len(other_df) > 0:
                other_row = other_df.iloc[0]
                # 首先添加doOpt和entity.id字段
                for field in ['doOpt', 'entity.id']:
                    if field in field_mapping:
                        config = field_mapping[field]
                        excel_col = config.get('excel_column')
                        default_val = config.get('default_value', '')
                        
                        if excel_col and excel_col in other_df.columns:
                            value = str(other_row.get(excel_col, default_val))
                            if pd.isna(value) or value == 'nan':
                                value = default_val
                        else:
                            value = default_val
                        
                        data[field] = value
                
                # 添加额外的实体字段（与template.json格式一致）
                data["exEntity.id"] = ""
                data["exEntity.elderId"] = ""
                data["exEntity.elderNo"] = ""
                
                # 处理其他基本信息字段
                for field, config in field_mapping.items():
                    if (field.startswith('entity.') or field in ['fromOutHouse', 'outHouseId']) and field != 'entity.id':
                        excel_col = config.get('excel_column')
                        default_val = config.get('default_value', '')
                        
                        if excel_col and excel_col in other_df.columns:
                            value = str(other_row.get(excel_col, default_val))
                            if pd.isna(value) or value == 'nan':
                                value = default_val
                        else:
                            value = default_val
                            
                        # 处理特殊字段
                        if field == 'entity.intoDate' and (not value or value == 'nan'):
                            value = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                            
                        data[field] = value
            else:
                # 如果没有基本信息，使用默认值
                for field, config in field_mapping.items():
                    if (field.startswith('entity.') or field in ['doOpt', 'entity.id', 'fromOutHouse', 'outHouseId']):
                        data[field] = config.get('default_value', '')
                
                # 添加额外的实体字段
                data["exEntity.id"] = ""
                data["exEntity.elderId"] = ""
                data["exEntity.elderNo"] = ""
            
            # 在entity.manufAddress1后添加空的goodsList字段
            data["goodsList"] = ""

            # 处理商品明细
            if len(goods_df) > 0:
                # 创建一个临时字典来存储所有商品项
                all_items = []
                total_rows = len(goods_df)
                
                for index, row in goods_df.iterrows():
                    # 更新进度条
                    progress = 20 + (index / total_rows * 60)
                    self.progress_var.set(progress)
                    self.window.update()
                    
                    # 创建临时字典存储当前商品的所有字段值
                    item_data = OrderedDict()
                    
                    # 处理每个映射字段
                    for field, config in field_mapping.items():
                        # 只处理商品相关字段
                        if not (field.startswith('entity.') or field in ['doOpt', 'fromOutHouse', 'outHouseId']):
                            excel_col = config.get('excel_column')
                            default_val = config.get('default_value', '')
                            
                            if excel_col and excel_col in goods_df.columns:
                                value = str(row.get(excel_col, default_val))
                                if pd.isna(value) or value == 'nan':
                                    value = default_val
                            else:
                                value = default_val
                                
                            item_data[field] = value
                    
                    # 如果有商品名称，尝试自动填充ID、编号和单位
                    if item_data.get('goodsCnName'):
                        goods_name = item_data.get('goodsCnName')
                        goods_info = goods_mapper.get_goods_info(goods_name)
                        
                        # 如果找到匹配的商品信息，填充ID和编号
                        if goods_info['id'] and not item_data.get('goodsId'):
                            item_data['goodsId'] = goods_info['id']
                            self.log(f"自动填充商品ID: {goods_name} -> {goods_info['id']}")
                            
                        if goods_info['no'] and not item_data.get('goodsNo'):
                            item_data['goodsNo'] = goods_info['no']
                            self.log(f"自动填充商品编号: {goods_name} -> {goods_info['no']}")
                            
                        # 自动填充单位信息
                        if goods_info['unit'] and (not item_data.get('unit') or item_data.get('unit') == ""):
                            item_data['unit'] = goods_info['unit']
                            self.log(f"自动填充商品单位: {goods_name} -> {goods_info['unit']}")
                    
                    # 添加额外的商品字段
                    # 移除isSplitUnit字段
                    item_data["splitUnit"] = ""
                    item_data["splitNum"] = ""
                    item_data["notes"] = row.get("备注", "")
                    if pd.isna(item_data["notes"]) or item_data["notes"] == 'nan':
                        item_data["notes"] = ""
                    
                    # 确保expireDate字段存在
                    if "expireDate" not in item_data:
                        item_data["expireDate"] = ""
                    
                    # 将当前商品项添加到列表中
                    all_items.append((index, item_data))
                
                # 按照test.json中的字段顺序添加到data字典中
                field_order = [
                    "goodsId", "goodsNo", "goodsCnName", "batchNo", "productionDate", 
                    "manufacturer", "expireDate", "planNum", "unit", "price", 
                    "subMoney", "splitUnit", "splitNum", "notes"
                ]
                
                # 为每个商品项添加所有字段，保持顺序一致
                for index, item_data in all_items:
                    for field in field_order:
                        if field in item_data:
                            data[f"recList[{index}].{field}"] = item_data[field]
            
            # 对数据进行后处理和格式化
            self.progress_var.set(70)
            self.log("正在格式化数据...")
            
            # 确保所有必需的字段都存在
            for field, config in field_mapping.items():
                if not field.startswith('recList[') and field not in ['goodsId', 'goodsNo', 'goodsCnName', 'planNum', 'unit', 'price', 'subMoney', 'batchNo', 'productionDate', 'manufacturer', 'expireDate', 'isSplitUnit']:  # 排除isSplitUnit字段
                    if field not in data:
                        data[field] = config.get('default_value', '')
            
            # 添加items字段，包含所有商品信息的副本
            if len(goods_df) > 0:
                self.log("正在添加items字段...", "info")
                items = []
                # 从all_items中提取商品信息
                for index, item_data in all_items:
                    item_copy = OrderedDict()
                    for field in field_order:
                        if field in item_data:
                            # 保留recList[index].前缀
                            prefixed_field = f"recList[{index}].{field}"
                            item_copy[prefixed_field] = item_data[field]
                    items.append(item_copy)
                # 将items添加到data字典的最后
                data["items"] = items

            self.progress_var.set(80)
            self.log("正在保存JSON文件...", "info")
            
            # 保存JSON文件，使用自定义的JSONEncoder来保持字段顺序
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.progress_var.set(100)
            self.log(f"转换完成！JSON文件已保存至: {json_path}", "success")

            # 创建JSON预览窗口
            self.current_json_window = tk.Toplevel(self.window)
            self.current_json_window.title("JSON预览")
            self.current_json_window.geometry("800x600")

            # 创建文本框和滚动条
            text_frame = ttk.Frame(self.current_json_window, padding=10)
            text_frame.pack(fill=tk.BOTH, expand=True)

            scroll = ttk.Scrollbar(text_frame)
            scroll.pack(side=tk.RIGHT, fill=tk.Y)

            text = tk.Text(text_frame, yscrollcommand=scroll.set, wrap=tk.NONE)
            text.pack(fill=tk.BOTH, expand=True)
            scroll.config(command=text.yview)

            # 添加复制按钮
            def copy_json():
                self.current_json_window.clipboard_clear()
                self.current_json_window.clipboard_append(text.get(1.0, tk.END))
                messagebox.showinfo("成功", "JSON内容已复制到剪贴板！")
                # 复制成功后关闭JSON预览窗口
                self.current_json_window.destroy()

            copy_button = ttk.Button(self.current_json_window, text="复制JSON", command=copy_json, style='Custom.TButton')
            copy_button.pack(pady=10)

            # 显示格式化的JSON
            text.insert(tk.END, json.dumps(data, ensure_ascii=False, indent=2))

            # 检查是否有缺失的物品
            self._check_missing_items(goods_df, goods_mapper, excel_path, json_path, other_df, field_mapping)

            messagebox.showinfo("成功", "转换完成！")
            
        except Exception as e:
            self.log(f"错误: {str(e)}", "error")
            messagebox.showerror("错误", f"转换过程中出现错误：{str(e)}")
            
        finally:
            self.convert_button.configure(state='normal')
            self.status_var.set("就绪")
            
    def open_item_editor(self):
        """打开物品编辑器窗口"""
        try:
            # 创建物品编辑器窗口，传入回调函数用于保存后重新加载数据
            editor = ItemEditor(self.window, callback=self.reload_goods_mapper)
            # 设置为模态窗口
            editor.window.transient(self.window)
            editor.window.grab_set()
            self.window.wait_window(editor.window)
        except Exception as e:
            messagebox.showerror("错误", f"打开物品编辑器时出现错误：{str(e)}")
    
    def reload_goods_mapper(self):
        """重新加载商品映射数据"""
        try:
            self.log("正在重新加载商品映射关系...", "info")
            # 重新初始化商品映射器
            goods_mapper = GoodsMapper()
            self.log(f"已成功重新加载商品映射关系，共 {len(goods_mapper.get_all_mappings())} 个物品", "success")
        except Exception as e:
            self.log(f"重新加载商品映射关系时出错: {str(e)}", "error")
    
    def open_batch_converter(self):
        """打开批量转换器"""
        try:
            # 加载物品数据
            item_data = {}
            if os.path.exists('item.json'):
                with open('item.json', 'r', encoding='utf-8') as f:
                    item_data = json.load(f)
            
            # 创建批量转换器窗口
            batch_converter = BatchConverter(self.window, item_data)
            # 设置为模态窗口
            batch_converter.window.transient(self.window)
            batch_converter.window.grab_set()
            self.window.wait_window(batch_converter.window)
        except Exception as e:
            messagebox.showerror("错误", f"打开批量转换器时出现错误：{str(e)}")
    
    def _check_missing_items(self, goods_df, goods_mapper, excel_path, json_path, other_df, field_mapping):
        """检查缺失的物品并显示匹配窗口"""
        try:
            if len(goods_df) == 0:
                return
            
            # 获取Excel中的所有物品名称
            excel_items = []
            for index, row in goods_df.iterrows():
                goods_name = str(row.get('商品名称', ''))
                if goods_name and goods_name != 'nan' and goods_name.strip():
                    excel_items.append(goods_name.strip())
            
            # 获取item.json中的所有物品
            available_items = goods_mapper.get_all_mappings()
            
            # 找出缺失的物品
            missing_items = ItemMatcher.find_missing_items(excel_items, available_items)
            
            if missing_items:
                self.log(f"发现 {len(missing_items)} 个物品在item.json中不存在: {', '.join(missing_items)}", "warning")
                
                # 关闭当前的JSON预览窗口，避免混淆
                if self.current_json_window and self.current_json_window.winfo_exists():
                    self.current_json_window.destroy()
                    self.current_json_window = None
                    self.log("已关闭JSON预览窗口，准备显示物品匹配窗口", "info")
                
                # 显示物品匹配窗口
                def on_reconvert(replacements):
                    self.log(f"用户选择了 {len(replacements)} 个替代物品，开始重新转换...", "info")
                    self._reconvert_with_replacements(replacements)
                
                ItemMatcher.show_matcher_window(self.window, missing_items, available_items, on_reconvert)
            else:
                self.log("所有物品都在item.json中存在", "success")
                
        except Exception as e:
            self.log(f"检查缺失物品时出错: {str(e)}", "error")
    
    def _reconvert_with_replacements(self, replacements):
        """使用替代物品重新转换"""
        try:
            if not all([self.last_excel_path, self.last_json_path, self.last_goods_df is not None, 
                       self.last_other_df is not None, self.last_field_mapping, self.last_goods_mapper]):
                messagebox.showerror("错误", "缺少重新转换所需的数据")
                return
            
            self.log("开始使用替代物品重新转换...", "info")
            self.progress_var.set(0)
            self.status_var.set("重新转换中...")
            self.convert_button.configure(state='disabled')
            self.window.update()
            
            # 创建修改后的goods_df副本
            modified_goods_df = self.last_goods_df.copy()
            
            # 应用替换
            for original_name, replacement_name in replacements.items():
                mask = modified_goods_df['商品名称'] == original_name
                modified_goods_df.loc[mask, '商品名称'] = replacement_name
                self.log(f"替换物品: {original_name} -> {replacement_name}", "info")
            
            # 执行转换逻辑（复制自convert方法的核心逻辑）
            data = self._perform_conversion(modified_goods_df, self.last_other_df, self.last_field_mapping, self.last_goods_mapper)
            
            # 保存JSON文件
            with open(self.last_json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.progress_var.set(100)
            self.log(f"重新转换完成！JSON文件已保存至: {self.last_json_path}", "success")
            
            # 创建JSON预览窗口
            self._show_json_preview(data)
            
            messagebox.showinfo("成功", "使用替代物品重新转换完成！")
            
        except Exception as e:
            self.log(f"重新转换时出错: {str(e)}", "error")
            messagebox.showerror("错误", f"重新转换过程中出现错误：{str(e)}")
        finally:
            self.convert_button.configure(state='normal')
            self.status_var.set("就绪")
    
    def _perform_conversion(self, goods_df, other_df, field_mapping, goods_mapper):
        """执行转换的核心逻辑"""
        # 使用OrderedDict来保持字段顺序
        data = OrderedDict()
        
        # 处理基本信息
        if len(other_df) > 0:
            other_row = other_df.iloc[0]
            # 首先添加doOpt和entity.id字段
            for field in ['doOpt', 'entity.id']:
                if field in field_mapping:
                    config = field_mapping[field]
                    excel_col = config.get('excel_column')
                    default_val = config.get('default_value', '')
                    
                    if excel_col and excel_col in other_df.columns:
                        value = str(other_row.get(excel_col, default_val))
                        if pd.isna(value) or value == 'nan':
                            value = default_val
                    else:
                        value = default_val
                    
                    data[field] = value
            
            # 添加额外的实体字段（与template.json格式一致）
            data["exEntity.id"] = ""
            data["exEntity.elderId"] = ""
            data["exEntity.elderNo"] = ""
            
            # 处理其他基本信息字段
            for field, config in field_mapping.items():
                if (field.startswith('entity.') or field in ['fromOutHouse', 'outHouseId']) and field != 'entity.id':
                    excel_col = config.get('excel_column')
                    default_val = config.get('default_value', '')
                    
                    if excel_col and excel_col in other_df.columns:
                        value = str(other_row.get(excel_col, default_val))
                        if pd.isna(value) or value == 'nan':
                            value = default_val
                    else:
                        value = default_val
                        
                    # 处理特殊字段
                    if field == 'entity.intoDate' and (not value or value == 'nan'):
                        value = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                        
                    data[field] = value
        else:
            # 如果没有基本信息，使用默认值
            for field, config in field_mapping.items():
                if (field.startswith('entity.') or field in ['doOpt', 'entity.id', 'fromOutHouse', 'outHouseId']):
                    data[field] = config.get('default_value', '')
            
            # 添加额外的实体字段
            data["exEntity.id"] = ""
            data["exEntity.elderId"] = ""
            data["exEntity.elderNo"] = ""
        
        # 在entity.manufAddress1后添加空的goodsList字段
        data["goodsList"] = ""

        # 处理商品明细
        if len(goods_df) > 0:
            # 创建一个临时字典来存储所有商品项
            all_items = []
            total_rows = len(goods_df)
            
            for index, row in goods_df.iterrows():
                # 更新进度条
                progress = 20 + (index / total_rows * 60)
                self.progress_var.set(progress)
                self.window.update()
                
                # 创建临时字典存储当前商品的所有字段值
                item_data = OrderedDict()
                
                # 处理每个映射字段
                for field, config in field_mapping.items():
                    # 只处理商品相关字段
                    if not (field.startswith('entity.') or field in ['doOpt', 'fromOutHouse', 'outHouseId']):
                        excel_col = config.get('excel_column')
                        default_val = config.get('default_value', '')
                        
                        if excel_col and excel_col in goods_df.columns:
                            value = str(row.get(excel_col, default_val))
                            if pd.isna(value) or value == 'nan':
                                value = default_val
                        else:
                            value = default_val
                            
                        item_data[field] = value
                
                # 如果有商品名称，尝试自动填充ID、编号和单位
                if item_data.get('goodsCnName'):
                    goods_name = item_data.get('goodsCnName')
                    goods_info = goods_mapper.get_goods_info(goods_name)
                    
                    # 如果找到匹配的商品信息，填充ID和编号
                    if goods_info['id'] and not item_data.get('goodsId'):
                        item_data['goodsId'] = goods_info['id']
                        self.log(f"自动填充商品ID: {goods_name} -> {goods_info['id']}")
                        
                    if goods_info['no'] and not item_data.get('goodsNo'):
                        item_data['goodsNo'] = goods_info['no']
                        self.log(f"自动填充商品编号: {goods_name} -> {goods_info['no']}")
                        
                    # 自动填充单位信息
                    if goods_info['unit'] and (not item_data.get('unit') or item_data.get('unit') == ""):
                        item_data['unit'] = goods_info['unit']
                        self.log(f"自动填充商品单位: {goods_name} -> {goods_info['unit']}")
                
                # 添加额外的商品字段
                item_data["splitUnit"] = ""
                item_data["splitNum"] = ""
                item_data["notes"] = row.get("备注", "")
                if pd.isna(item_data["notes"]) or item_data["notes"] == 'nan':
                    item_data["notes"] = ""
                
                # 确保expireDate字段存在
                if "expireDate" not in item_data:
                    item_data["expireDate"] = ""
                
                # 将当前商品项添加到列表中
                all_items.append((index, item_data))
            
            # 按照test.json中的字段顺序添加到data字典中
            field_order = [
                "goodsId", "goodsNo", "goodsCnName", "batchNo", "productionDate", 
                "manufacturer", "expireDate", "planNum", "unit", "price", 
                "subMoney", "splitUnit", "splitNum", "notes"
            ]
            
            # 为每个商品项添加所有字段，保持顺序一致
            for index, item_data in all_items:
                for field in field_order:
                    if field in item_data:
                        data[f"recList[{index}].{field}"] = item_data[field]
        
        # 对数据进行后处理和格式化
        self.progress_var.set(70)
        self.log("正在格式化数据...")
        
        # 确保所有必需的字段都存在
        for field, config in field_mapping.items():
            if not field.startswith('recList[') and field not in ['goodsId', 'goodsNo', 'goodsCnName', 'planNum', 'unit', 'price', 'subMoney', 'batchNo', 'productionDate', 'manufacturer', 'expireDate', 'isSplitUnit']:
                if field not in data:
                    data[field] = config.get('default_value', '')
        
        # 添加items字段，包含所有商品信息的副本
        if len(goods_df) > 0:
            self.log("正在添加items字段...", "info")
            items = []
            # 从all_items中提取商品信息
            for index, item_data in all_items:
                item_copy = OrderedDict()
                for field in field_order:
                    if field in item_data:
                        # 保留recList[index].前缀
                        prefixed_field = f"recList[{index}].{field}"
                        item_copy[prefixed_field] = item_data[field]
                items.append(item_copy)
            # 将items添加到data字典的最后
            data["items"] = items

        self.progress_var.set(80)
        self.log("正在保存JSON文件...", "info")
        
        return data
    
    def _show_json_preview(self, data):
        """显示JSON预览窗口"""
        # 关闭之前的JSON预览窗口（如果存在）
        if self.current_json_window and self.current_json_window.winfo_exists():
            self.current_json_window.destroy()
        
        # 创建JSON预览窗口
        self.current_json_window = tk.Toplevel(self.window)
        self.current_json_window.title("JSON预览")
        self.current_json_window.geometry("800x600")

        # 创建文本框和滚动条
        text_frame = ttk.Frame(self.current_json_window, padding=10)
        text_frame.pack(fill=tk.BOTH, expand=True)

        scroll = ttk.Scrollbar(text_frame)
        scroll.pack(side=tk.RIGHT, fill=tk.Y)

        text = tk.Text(text_frame, yscrollcommand=scroll.set, wrap=tk.NONE)
        text.pack(fill=tk.BOTH, expand=True)
        scroll.config(command=text.yview)

        # 添加复制按钮
        def copy_json():
            self.current_json_window.clipboard_clear()
            self.current_json_window.clipboard_append(text.get(1.0, tk.END))
            messagebox.showinfo("成功", "JSON内容已复制到剪贴板！")
            # 复制成功后关闭JSON预览窗口
            self.current_json_window.destroy()

        copy_button = ttk.Button(self.current_json_window, text="复制JSON", command=copy_json, style='Custom.TButton')
        copy_button.pack(pady=10)

        # 显示格式化的JSON
        text.insert(tk.END, json.dumps(data, ensure_ascii=False, indent=2))
    
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = ExcelToJsonConverter()
    app.run()