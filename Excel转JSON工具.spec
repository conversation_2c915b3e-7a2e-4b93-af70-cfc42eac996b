# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('template.xlsx', '.'), 
        ('mapping.json', '.'), 
        ('template.json', '.'), 
        ('item.json', '.'),
        ('id.json', '.'),
        ('batch_config.json', '.')
    ],
    hiddenimports=[
        'pandas', 
        'openpyxl', 
        'difflib', 
        'goods_mapping', 
        'item_editor', 
        'item_matcher',
        'batch_converter',
        'template',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'datetime',
        'collections',
        'json',
        'os',
        'copy',
        'threading',
        'queue'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Excel转JSON工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['icon.ico'],
)
