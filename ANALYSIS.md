# Excel转JSON工具技术文档

## 项目概述

这是一个基于Python开发的Excel转JSON转换工具，主要用于将特定格式的Excel数据转换为标准化的JSON格式。该工具提供了图形用户界面，支持数据预览、自动字段映射和商品信息自动填充等功能。

## 系统架构

### 核心组件

1. **GUI界面层** (`main.py`)
   - 基于tkinter实现
   - 提供文件选择、数据预览、转换控制等功能
   - 实时显示转换进度和操作日志

2. **数据处理层**
   - `goods_mapping.py`: 商品信息映射和管理
   - `template.py`: Excel模板生成和管理
   - `mapping.json`: 字段映射配置

3. **数据存储**
   - Excel文件: 输入数据源
   - JSON文件: 输出结果
   - 配置文件: 映射关系和模板定义

### 文件结构
```
ExcelToJsonTool2/
├── main.py              # 主程序入口
├── goods_mapping.py     # 商品映射模块
├── template.py          # 模板生成器
├── mapping.json         # 字段映射配置
├── template.xlsx        # Excel模板文件
├── test.json           # 测试数据
├── item.json           # 商品映射缓存
└── requirements.txt     # 项目依赖
```

## 功能实现

### 1. GUI界面实现 (main.py)

#### 主要组件
- 文件选择区域
  - Excel文件输入框
  - JSON保存路径选择
  - 文件浏览按钮
- 操作控制区
  - 数据预览按钮
  - 开始转换按钮
  - 清除日志按钮
- 状态显示
  - 进度条
  - 日志显示框
  - 状态栏

#### 核心功能
```python
class ExcelToJsonConverter:
    def __init__(self):
        # 初始化GUI窗口
        # 配置界面组件
        
    def preview_data(self):
        # 实现Excel数据预览
        # 支持多工作表显示
        
    def convert(self):
        # 执行转换流程
        # 处理异常情况
```

### 2. 商品映射实现 (goods_mapping.py)

#### 核心功能
```python
class GoodsMapper:
    def __init__(self):
        self.goods_map = {}
        
    def load_mappings(self):
        # 从配置文件加载映射
        
    def get_goods_info(self, goods_name):
        # 获取商品详细信息
```

#### 数据结构
```json
{
    "商品名称": {
        "id": "商品ID",
        "no": "商品编号",
        "unit": "单位"
    }
}
```

### 3. 模板管理 (template.py)

#### Excel模板结构
- **基本信息工作表**
  - 入库单号
  - 入库日期
  - 仓库信息
  - 供应商信息
  
- **商品明细工作表**
  - 商品基本信息
  - 数量和价格
  - 批次信息
  
- **填写说明工作表**
  - 字段说明
  - 格式要求
  - 示例数据

### 4. 字段映射配置 (mapping.json)

#### 映射结构
```json
{
  "excel_to_json_mapping": {
    "字段名": {
      "excel_column": "Excel列名",
      "default_value": "默认值",
      "type": "数据类型"
    }
  }
}
```

#### 主要映射类型
1. 基本信息字段
2. 商品信息字段
3. 系统字段

## 数据处理流程

### 1. 输入处理
1. 读取Excel文件
2. 验证数据格式
3. 提取工作表数据

### 2. 数据转换
1. 字段映射转换
2. 商品信息补充
3. 数据验证与格式化

### 3. 输出生成
1. JSON数据组装
2. 字段顺序排序
3. 文件保存

## 异常处理机制

### 1. 文件操作异常
- 文件不存在
- 权限不足
- 格式错误

### 2. 数据处理异常
- 字段缺失
- 格式不匹配
- 数据类型错误

### 3. 映射异常
- 配置缺失
- 映射规则错误
- 默认值处理

## 项目依赖

```
tkinter         # GUI界面
pandas         # Excel数据处理
json           # JSON数据处理
openpyxl       # Excel文件操作
```

## 最佳实践

### 1. 数据准备
- 使用提供的模板文件
- 按格式要求填写数据
- 确保必填字段完整

### 2. 转换操作
- 预览数据确认无误
- 选择正确的保存路径
- 查看转换日志

### 3. 结果验证
- 检查JSON格式
- 验证字段映射
- 确认数据完整性