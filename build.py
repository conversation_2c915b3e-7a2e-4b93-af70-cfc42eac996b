import PyInstaller.__main__
import os

# 获取当前脚本所在目录
base_path = os.path.dirname(os.path.abspath(__file__))

# 定义资源文件
data_files = [
    (os.path.join(base_path, 'template.xlsx'), '.'),
    (os.path.join(base_path, 'mapping.json'), '.'),
    (os.path.join(base_path, 'template.json'), '.'),
    (os.path.join(base_path, 'item.json'), '.'),
    (os.path.join(base_path, 'id.json'), '.'),
    (os.path.join(base_path, 'batch_config.json'), '.'),
    (os.path.join(base_path, 'icon.ico'), '.'),
]

# 构建PyInstaller命令参数
pyinstaller_args = [
    'main.py',                          # 主脚本
    '--name=Excel转JSON工具',            # 应用名称
    '--onefile',                        # 打包成单个文件
    '--windowed',                       # 使用窗口模式（不显示控制台）
    '--icon=icon.ico',                  # 使用自定义图标
    '--clean',                          # 清理临时文件
    '--add-data=template.xlsx;.',       # 添加资源文件
    '--add-data=mapping.json;.',
    '--add-data=template.json;.',
    '--add-data=item.json;.',
    '--add-data=id.json;.',
    '--add-data=batch_config.json;.',
    '--add-data=icon.ico;.',
    '--hidden-import=pandas',           # 添加隐式导入
    '--hidden-import=openpyxl',
    '--hidden-import=difflib',          # 添加相似度匹配模块
    '--hidden-import=goods_mapping',    # 添加项目模块
    '--hidden-import=item_editor',
    '--hidden-import=item_matcher',     # 添加新的物品匹配模块
    '--hidden-import=batch_converter',  # 添加批量转换器模块
    '--hidden-import=template',         # 添加模板模块
    '--hidden-import=tkinter.ttk',
    '--hidden-import=tkinter.messagebox',
    '--hidden-import=tkinter.filedialog',
    '--hidden-import=datetime',
    '--hidden-import=collections',
    '--hidden-import=json',
    '--hidden-import=copy',
    '--hidden-import=threading',
    '--hidden-import=queue'
]

# 执行PyInstaller打包
PyInstaller.__main__.run(pyinstaller_args)

print("\n打包完成！可执行文件位于 dist 文件夹中。")