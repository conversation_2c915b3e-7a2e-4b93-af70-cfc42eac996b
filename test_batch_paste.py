#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试批量粘贴表格功能
"""

import re
from datetime import datetime

def test_parse_tables():
    """测试解析表格数据"""
    
    # 读取实例.txt文件
    with open('实例.txt', 'r', encoding='utf-8') as f:
        text = f.read()
    
    lines = text.split('\n')
    
    # 查找所有表格标题行
    table_headers = []
    for i, line in enumerate(lines):
        # 匹配表格标题格式：汕尾城区颐养院送货单（2025.06.10）（备注）
        # 使用findall来找到一行中的所有匹配项
        matches = re.findall(r'汕尾城区颐养院送货单（(\d{4}\.\d{2}\.\d{2})）（([^）]+)）', line)
        for match in matches:
            date_str = match[0]
            warehouse_note = match[1]
            if '总单' not in warehouse_note:  # 忽略总单
                table_headers.append({
                    'line_index': i,
                    'date': date_str,
                    'warehouse_note': warehouse_note
                })
    
    print(f"找到 {len(table_headers)} 个表格标题:")
    for header in table_headers:
        print(f"  - 行 {header['line_index']}: {header['warehouse_note']} ({header['date']})")
    
    # 解析每个表格的商品数据
    for i, header in enumerate(table_headers):
        print(f"\n=== 解析表格: {header['warehouse_note']} ===")
        
        # 确定表格数据范围
        start_line = header['line_index'] + 1
        end_line = len(lines)
        if i + 1 < len(table_headers):
            end_line = table_headers[i + 1]['line_index']
        
        print(f"数据范围: 行 {start_line} 到 {end_line-1}")
        
        # 查找数据开始行
        data_start = -1
        for j in range(start_line, end_line):
            if j < len(lines) and '名称及规格' in lines[j] and '数量' in lines[j] and '单价' in lines[j]:
                data_start = j + 1
                print(f"数据开始行: {data_start}")
                break
        
        if data_start == -1:
            print("未找到数据开始行")
            continue
        
        # 解析商品数据
        goods_count = 0
        warehouse_note = header['warehouse_note']
        
        for j in range(data_start, end_line):
            if j >= len(lines):
                break
                
            line = lines[j].strip()
            if not line or '合计金额' in line or '送货人' in line:
                break
            
            # 分割数据（使用制表符分割）
            parts = line.split('\t')
            
            # 根据仓库备注确定使用哪一组数据
            name = unit = quantity = price = ""
            
            if warehouse_note in ['非城区特困长者', '员工']:
                # 左侧表格或第一个表格
                if len(parts) >= 5:
                    name = parts[0].strip()
                    unit = parts[1].strip()
                    quantity = parts[2].strip()
                    price = parts[3].strip()
            else:
                # 右侧表格或第二个表格
                # 寻找右侧数据的起始位置
                right_start = -1
                for k in range(5, len(parts)):
                    if parts[k].strip():
                        right_start = k
                        break
                
                if right_start != -1 and right_start + 3 < len(parts):
                    name = parts[right_start].strip()
                    unit = parts[right_start + 1].strip()
                    quantity = parts[right_start + 2].strip()
                    price = parts[right_start + 3].strip()
            
            # 验证数据
            if name and quantity and price:
                try:
                    float(quantity)
                    float(price)
                    goods_count += 1
                    print(f"  商品 {goods_count}: {name} - {quantity}{unit} - ¥{price}")
                except ValueError:
                    print(f"  跳过无效数据: {line}")
        
        print(f"总共解析到 {goods_count} 个商品")

if __name__ == "__main__":
    test_parse_tables()