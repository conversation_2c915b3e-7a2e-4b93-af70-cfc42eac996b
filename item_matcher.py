# 物品匹配模块
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import difflib
from collections import OrderedDict

class ItemMatcherWindow:
    def __init__(self, parent, missing_items, available_items, on_reconvert_callback):
        """
        初始化物品匹配窗口
        
        Args:
            parent: 父窗口
            missing_items: 缺失的物品列表
            available_items: 可用的物品字典 {name: {id, no, unit}}
            on_reconvert_callback: 重新转换的回调函数
        """
        self.parent = parent
        self.missing_items = missing_items
        self.available_items = available_items
        self.on_reconvert_callback = on_reconvert_callback
        self.replacements = {}  # 存储用户选择的替换映射
        
        self.create_window()
        
    def create_window(self):
        """创建匹配窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("物品匹配 - 选择替代物品")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 设置窗口居中
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="发现以下物品在item.json中不存在，请为每个物品选择最接近的替代物品：", 
                               font=('微软雅黑', 12, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # 提示信息
        info_label = ttk.Label(main_frame, text="提示：您可以选择\"不替换\"跳过某个物品，或使用\"自动匹配最相似\"功能快速匹配", 
                              font=('微软雅黑', 9), foreground='gray')
        info_label.pack(pady=(0, 10))
        
        # 提示信息
        info_label = ttk.Label(main_frame, text="提示：您可以选择\"不替换\"跳过某个物品，或使用\"自动匹配最相似\"功能快速匹配", 
                              font=('微软雅黑', 9), foreground='gray')
        info_label.pack(pady=(0, 10))
        
        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 为每个缺失的物品创建选择界面
        self.combo_vars = {}
        for i, missing_item in enumerate(self.missing_items):
            self.create_item_selection_row(scrollable_frame, missing_item, i)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 自动匹配按钮
        auto_match_btn = ttk.Button(button_frame, text="自动匹配最相似", command=self.auto_match)
        auto_match_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重新转换按钮
        reconvert_btn = ttk.Button(button_frame, text="使用选择的替代物品重新转换", command=self.reconvert)
        reconvert_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.window.destroy)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
    def create_item_selection_row(self, parent, missing_item, index):
        """为每个缺失物品创建选择行"""
        row_frame = ttk.LabelFrame(parent, text=f"缺失物品 {index + 1}", padding=10)
        row_frame.pack(fill=tk.X, pady=5)
        
        # 缺失物品名称
        missing_label = ttk.Label(row_frame, text=f"Excel中的物品: {missing_item}", 
                                 font=('微软雅黑', 10, 'bold'), foreground='red')
        missing_label.pack(anchor=tk.W)
        
        # 选择替代物品
        select_frame = ttk.Frame(row_frame)
        select_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(select_frame, text="选择替代物品:").pack(side=tk.LEFT)
        
        # 下拉框
        combo_var = tk.StringVar()
        combo = ttk.Combobox(select_frame, textvariable=combo_var, width=40, state="readonly")
        
        # 获取推荐的相似物品
        similar_items = self.get_similar_items(missing_item)
        combo['values'] = ["不替换"] + similar_items
        combo.set("不替换")  # 默认选择
        
        combo.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        self.combo_vars[missing_item] = combo_var
        
        # 显示推荐理由
        if similar_items:
            reason_label = ttk.Label(row_frame, text=f"推荐: {similar_items[0]} (相似度最高)", 
                                   font=('微软雅黑', 9), foreground='blue')
            reason_label.pack(anchor=tk.W, pady=(2, 0))
    
    def get_similar_items(self, missing_item, top_n=5):
        """获取与缺失物品最相似的物品列表"""
        available_names = list(self.available_items.keys())
        
        # 使用difflib计算相似度
        similar_items = difflib.get_close_matches(missing_item, available_names, n=top_n, cutoff=0.1)
        
        # 如果没有找到相似的，返回前几个物品作为备选
        if not similar_items:
            similar_items = available_names[:top_n]
        
        return similar_items
    
    def auto_match(self):
        """自动匹配最相似的物品"""
        for missing_item in self.missing_items:
            similar_items = self.get_similar_items(missing_item, 1)
            if similar_items:
                self.combo_vars[missing_item].set(similar_items[0])
        
        messagebox.showinfo("自动匹配完成", "已为所有缺失物品自动选择最相似的替代物品")
    
    def reconvert(self):
        """使用选择的替代物品重新转换"""
        # 收集用户的选择
        replacements = {}
        for missing_item, combo_var in self.combo_vars.items():
            selected = combo_var.get()
            if selected and selected != "不替换":
                replacements[missing_item] = selected
        
        if not replacements:
            messagebox.showwarning("警告", "您没有选择任何替代物品，将跳过缺失的物品进行转换")
        
        # 关闭窗口
        self.window.destroy()
        
        # 调用重新转换回调
        self.on_reconvert_callback(replacements)


class ItemMatcher:
    """物品匹配器主类"""
    
    @staticmethod
    def find_missing_items(excel_items, available_items):
        """
        找出Excel中存在但item.json中不存在的物品
        
        Args:
            excel_items: Excel中的物品名称列表
            available_items: item.json中的物品字典
            
        Returns:
            缺失的物品名称列表
        """
        missing_items = []
        for item in excel_items:
            if item and item.strip() and item not in available_items:
                missing_items.append(item)
        
        return list(set(missing_items))  # 去重
    
    @staticmethod
    def show_matcher_window(parent, missing_items, available_items, on_reconvert_callback):
        """
        显示物品匹配窗口
        
        Args:
            parent: 父窗口
            missing_items: 缺失的物品列表
            available_items: 可用的物品字典
            on_reconvert_callback: 重新转换的回调函数
        """
        if not missing_items:
            return False
        
        ItemMatcherWindow(parent, missing_items, available_items, on_reconvert_callback)
        return True