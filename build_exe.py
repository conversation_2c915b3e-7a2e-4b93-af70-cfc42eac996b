#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
打包脚本 - 将项目打包为可执行文件
"""

import os
import sys
import subprocess
import shutil

def main():
    """主函数"""
    print("🚀 开始打包 Excel转JSON工具...")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller 安装完成")
    
    # 清理之前的构建文件
    if os.path.exists("build"):
        print("🧹 清理 build 目录...")
        shutil.rmtree("build")
    
    if os.path.exists("dist"):
        print("🧹 清理 dist 目录...")
        shutil.rmtree("dist")
    
    # 检查必要文件是否存在
    required_files = [
        "main.py",
        "batch_converter.py", 
        "goods_mapping.py",
        "item_editor.py",
        "item_matcher.py",
        "template.py",
        "template.xlsx",
        "mapping.json",
        "template.json",
        "item.json",
        "id.json",
        "batch_config.json",
        "icon.ico"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件检查完成")
    
    # 使用spec文件打包
    spec_file = "Excel转JSON工具.spec"
    if os.path.exists(spec_file):
        print(f"📦 使用 {spec_file} 进行打包...")
        cmd = [sys.executable, "-m", "PyInstaller", spec_file]
    else:
        print("📦 使用命令行参数进行打包...")
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--name=Excel转JSON工具",
            "--onefile",
            "--windowed",
            "--icon=icon.ico",
            "--clean",
            "--add-data=template.xlsx;.",
            "--add-data=mapping.json;.",
            "--add-data=template.json;.",
            "--add-data=item.json;.",
            "--add-data=id.json;.",
            "--add-data=batch_config.json;.",
            "--add-data=icon.ico;.",
            "--hidden-import=pandas",
            "--hidden-import=openpyxl",
            "--hidden-import=difflib",
            "--hidden-import=goods_mapping",
            "--hidden-import=item_editor",
            "--hidden-import=item_matcher",
            "--hidden-import=batch_converter",
            "--hidden-import=template",
            "--hidden-import=tkinter.ttk",
            "--hidden-import=tkinter.messagebox",
            "--hidden-import=tkinter.filedialog",
            "--hidden-import=datetime",
            "--hidden-import=collections",
            "--hidden-import=json",
            "--hidden-import=copy",
            "--hidden-import=threading",
            "--hidden-import=queue",
            "main.py"
        ]
    
    try:
        print("⏳ 正在打包，请稍候...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查生成的文件
            exe_path = os.path.join("dist", "Excel转JSON工具.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 可执行文件位置: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                
                # 创建发布目录
                release_dir = "release"
                if not os.path.exists(release_dir):
                    os.makedirs(release_dir)
                
                # 复制exe文件到发布目录
                release_exe = os.path.join(release_dir, "Excel转JSON工具.exe")
                shutil.copy2(exe_path, release_exe)
                print(f"📦 发布文件: {release_exe}")
                
                # 创建使用说明
                readme_content = """# Excel转JSON工具

## 使用说明

1. 双击运行 Excel转JSON工具.exe
2. 选择要转换的Excel文件
3. 配置转换参数
4. 点击转换按钮

## 功能特性

- Excel文件转JSON格式
- 批量转换功能
- 商品信息管理
- 模板配置
- 数据预览

## 注意事项

- 首次运行可能需要较长时间加载
- 确保Excel文件格式正确
- 转换后的JSON文件会保存在指定目录

## 技术支持

如有问题请联系开发团队。
"""
                
                readme_path = os.path.join(release_dir, "使用说明.txt")
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                print("📝 使用说明已生成")
                print(f"\n🎉 打包完成！发布文件位于 {release_dir} 目录")
                return True
            else:
                print("❌ 未找到生成的可执行文件")
                return False
        else:
            print("❌ 打包失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 打包过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✨ 打包成功完成！")
        input("按回车键退出...")
    else:
        print("\n💥 打包失败！")
        input("按回车键退出...")