# 商品名称与ID、编号映射模块
import json
import os
import sys
import traceback

class GoodsMapper:
    def __init__(self):
        self.goods_map = {}
        # 获取应用程序目录的多种可能路径
        self.app_dir = os.path.dirname(os.path.abspath(__file__))
        # 添加可能的备选路径，优先使用可执行文件所在目录
        self.possible_paths = [
            os.path.dirname(sys.executable),  # 可执行文件所在目录（优先）
            os.path.abspath(os.path.curdir),  # 当前工作目录
            self.app_dir,  # 当前脚本所在目录
            os.path.dirname(os.path.abspath(sys.argv[0])),  # 执行脚本的目录
            getattr(sys, '_MEIPASS', self.app_dir)  # PyInstaller打包时的临时目录
        ]
        print(f"初始化GoodsMapper，应用目录: {self.app_dir}")
        print(f"可能的路径列表: {self.possible_paths}")
        self.load_mappings()
    
    def load_mappings(self):
        """从test.json文件中加载商品映射关系并保存到item.json"""
        # 首先尝试从item.json加载，这是最常用的情况
        if self._load_from_item_json():
            return
            
        # 如果item.json加载失败，尝试从test.json加载
        for path in self.possible_paths:
            try:
                test_json_path = os.path.join(path, 'test.json')
                print(f"尝试从路径加载test.json: {test_json_path}")
                
                if os.path.exists(test_json_path):
                    print(f"找到test.json文件: {test_json_path}")
                    with open(test_json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self._extract_goods_info(data)
                    
                    # 将提取的商品信息保存到item.json
                    self._save_to_item_json()
                    return
            except Exception as e:
                print(f"从路径 {path} 加载test.json时出错: {str(e)}")
                traceback.print_exc()
                
        print("警告: 无法从任何位置加载商品映射数据，商品自动填充功能将不可用")
    
    def _extract_goods_info(self, data):
        """从JSON数据中提取商品信息"""
        # 提取recList中的商品信息
        index = 0
        while True:
            goods_id_key = f"recList[{index}].goodsId"
            goods_no_key = f"recList[{index}].goodsNo"
            goods_name_key = f"recList[{index}].goodsCnName"
            
            if goods_id_key not in data or goods_name_key not in data:
                break
                
            goods_id = data.get(goods_id_key, "")
            goods_no = data.get(goods_no_key, "")
            goods_name = data.get(goods_name_key, "")
            
            if goods_name and (goods_id or goods_no):
                # 尝试获取单位信息
                unit_key = f"recList[{index}].unit"
                unit = data.get(unit_key, "")
                
                self.goods_map[goods_name] = {
                    'id': goods_id,
                    'no': goods_no,
                    'unit': unit
                }
            
            index += 1
        
        # 也检查items数组中的商品信息
        if "items" in data and isinstance(data["items"], list):
            for item in data["items"]:
                # 在items中，每个项目都有完整的字段名，包括索引
                for key in item.keys():
                    if key.endswith(".goodsCnName"):
                        index_prefix = key.split(".")[0]  # 获取如 "recList[0]" 的前缀
                        goods_name = item.get(key, "")
                        goods_id = item.get(f"{index_prefix}.goodsId", "")
                        goods_no = item.get(f"{index_prefix}.goodsNo", "")
                        
                        if goods_name and (goods_id or goods_no):
                            # 尝试获取单位信息
                            unit = item.get(f"{index_prefix}.unit", "")
                            
                            self.goods_map[goods_name] = {
                                'id': goods_id,
                                'no': goods_no,
                                'unit': unit
                            }
    
    def _load_from_item_json(self):
        """直接从item.json加载商品映射关系，成功返回True，失败返回False"""
        for path in self.possible_paths:
            try:
                item_json_path = os.path.join(path, 'item.json')
                print(f"尝试从路径加载item.json: {item_json_path}")
                
                if os.path.exists(item_json_path):
                    print(f"找到item.json文件: {item_json_path}")
                    with open(item_json_path, 'r', encoding='utf-8') as f:
                        item_data = json.load(f)
                        for goods_name, info in item_data.items():
                            self.goods_map[goods_name] = {
                                'id': info.get('id', ''),
                                'no': info.get('no', ''),
                                'unit': info.get('unit', '')
                            }
                    print(f"已从item.json加载{len(self.goods_map)}个商品的映射关系")
                    return True
            except Exception as e:
                print(f"从路径 {path} 加载item.json时出错: {str(e)}")
                traceback.print_exc()
                
        print("警告: 未能找到或加载item.json文件")
        return False
    
    def get_goods_info(self, goods_name):
        """根据商品名称获取ID、编号和单位"""
        result = self.goods_map.get(goods_name, {'id': '', 'no': '', 'unit': ''})
        print(f"查询商品 '{goods_name}' 的信息: {result}")
        return result
    
    def get_all_mappings(self):
        """获取所有商品映射关系"""
        return self.goods_map

    def _save_to_item_json(self):
        """将商品映射关系保存到item.json文件"""
        if not self.goods_map:
            print("警告: 没有商品映射数据可保存")
            return
            
        # 尝试在所有可能的路径中保存
        for path in self.possible_paths:
            try:
                # 将商品映射关系转换为适合保存的格式
                item_data = {}
                for goods_name, info in self.goods_map.items():
                    # 确保unit字段始终存在，即使为空字符串
                    unit = info.get('unit', '')
                    item_data[goods_name] = {
                        'id': info['id'],
                        'no': info['no'],
                        'unit': unit
                    }
                    
                # 保存到item.json文件
                item_json_path = os.path.join(path, 'item.json')
                print(f"尝试保存item.json到路径: {item_json_path}")
                
                with open(item_json_path, 'w', encoding='utf-8') as f:
                    json.dump(item_data, f, ensure_ascii=False, indent=2)
                    
                print(f"已将{len(self.goods_map)}个商品的映射关系保存到item.json: {item_json_path}")
                return True
            except Exception as e:
                print(f"保存商品映射关系到路径 {path} 时出错: {str(e)}")
                traceback.print_exc()
                
        print("错误: 无法保存商品映射关系到任何路径")
        return False

# 测试代码
if __name__ == "__main__":
    mapper = GoodsMapper()
    print("商品映射关系:")
    for name, info in mapper.get_all_mappings().items():
        print(f"{name}: ID={info['id']}, 编号={info['no']}")