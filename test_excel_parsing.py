#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel格式解析问题
"""

def analyze_excel_format():
    """分析实例.md中的Excel格式"""
    print("=== 分析Excel格式数据 ===")
    
    with open('实例.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 分析员工表格部分（第25-50行）
    print("\n员工表格数据分析:")
    employee_start = 25  # 第26行开始（0-based是25）
    employee_end = 50
    
    for i in range(employee_start, min(employee_end, len(lines))):
        line = lines[i].rstrip('\n')
        if '名称及规格' in line or '合计金额' in line or '送货人' in line:
            continue
        if not line.strip():
            continue
            
        parts = line.split('\t')
        print(f"行{i+1}: {len(parts)}个部分")
        for j, part in enumerate(parts):
            if part.strip():
                print(f"  [{j}]: '{part.strip()}'")
            else:
                print(f"  [{j}]: (空)")
        
        # 分析左侧表格（员工）
        if len(parts) >= 5:
            left_name = parts[0].strip()
            left_quantity = parts[2].strip()
            if left_name and left_quantity:
                print(f"  左侧(员工): {left_name} - {left_quantity}")
        
        # 分析右侧表格（城区特困长者）
        right_start = -1
        for j in range(5, len(parts)):
            if parts[j].strip():
                right_start = j
                break
        
        if right_start != -1 and right_start + 2 < len(parts):
            right_name = parts[right_start].strip()
            right_quantity = parts[right_start + 2].strip()
            if right_name and right_quantity:
                print(f"  右侧(城区特困长者): {right_name} - {right_quantity}")
        
        print()

def test_improved_parsing():
    """测试改进的解析逻辑"""
    print("=== 测试改进的解析逻辑 ===")
    
    # 模拟员工表格的几行数据
    test_lines = [
        "肚肉\t斤\t1.1\t15\t16.50\t\t\t肚肉\t斤\t3.7\t15\t55.50",
        "瘦肉\t斤\t1.4\t19\t26.60\t\t\t瘦肉\t斤\t5.0\t19\t95.00",
        "\t\t\t\t\t\t\t排骨\t斤\t1.3\t27\t35.10",
        "\t\t\t\t\t\t\t豆干\t块\t35.0\t2.5\t87.50",
        "光鸡整只去爪\t斤\t3.5\t12.5\t43.75\t\t\t光鸡整只去爪\t斤\t15.5\t12.5\t193.75"
    ]
    
    print("左侧表格（员工）解析结果:")
    left_goods = parse_goods_improved(test_lines, is_left_table=True)
    for goods in left_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']}")
    
    print("\n右侧表格（城区特困长者）解析结果:")
    right_goods = parse_goods_improved(test_lines, is_left_table=False)
    for goods in right_goods:
        print(f"  {goods['name']}: {goods['quantity']} {goods['unit']}")

def parse_goods_improved(lines, is_left_table=True):
    """改进的商品解析函数"""
    goods = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 分割数据（使用制表符分割）
        parts = line.split('\t')
        
        # 根据表格位置确定使用哪一组数据
        if is_left_table:
            # 左侧表格：索引 0,1,2,3,4
            if len(parts) >= 5:
                name = parts[0].strip()
                unit = parts[1].strip()
                quantity = parts[2].strip()
                price = parts[3].strip()
                
                # 只有当名称和数量都不为空时才添加
                if name and quantity and quantity != '0':
                    try:
                        float(quantity)
                        float(price)
                        goods.append({
                            'name': name,
                            'quantity': quantity,
                            'price': price,
                            'unit': unit
                        })
                    except ValueError:
                        continue
        else:
            # 右侧表格：寻找右侧数据的起始位置
            # 通常在索引6或7开始（跳过左侧的5列和分隔列）
            right_start = -1
            for j in range(6, len(parts)):  # 从索引6开始查找
                if parts[j].strip():
                    right_start = j
                    break
            
            if right_start != -1 and right_start + 3 < len(parts):
                name = parts[right_start].strip()
                unit = parts[right_start + 1].strip()
                quantity = parts[right_start + 2].strip()
                price = parts[right_start + 3].strip()
                
                # 只有当名称和数量都不为空时才添加
                if name and quantity and quantity != '0':
                    try:
                        float(quantity)
                        float(price)
                        goods.append({
                            'name': name,
                            'quantity': quantity,
                            'price': price,
                            'unit': unit
                        })
                    except ValueError:
                        continue
    
    return goods

if __name__ == "__main__":
    analyze_excel_format()
    test_improved_parsing()
