#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清空功能
"""

import os
import tempfile
import json

def test_clear_output_directory():
    """测试清空输出目录功能"""
    print("=== 测试清空输出目录功能 ===")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时目录: {temp_dir}")
        
        # 创建一些测试文件
        test_files = [
            "table1.json",
            "table2.json", 
            "table3.json",
            "readme.txt"  # 非json文件，不应该被删除
        ]
        
        for filename in test_files:
            filepath = os.path.join(temp_dir, filename)
            if filename.endswith('.json'):
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump({"test": "data"}, f)
            else:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write("test content")
        
        print(f"创建了 {len(test_files)} 个测试文件")
        
        # 列出文件
        files_before = os.listdir(temp_dir)
        print(f"清空前的文件: {files_before}")
        
        # 模拟清空功能
        import glob
        json_files = glob.glob(os.path.join(temp_dir, "*.json"))
        deleted_count = 0
        for file_path in json_files:
            try:
                os.remove(file_path)
                deleted_count += 1
                print(f"删除文件: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"删除文件 {file_path} 失败: {str(e)}")
        
        # 列出剩余文件
        files_after = os.listdir(temp_dir)
        print(f"清空后的文件: {files_after}")
        print(f"删除了 {deleted_count} 个JSON文件")
        
        # 验证结果
        json_files_remaining = [f for f in files_after if f.endswith('.json')]
        non_json_files_remaining = [f for f in files_after if not f.endswith('.json')]
        
        if len(json_files_remaining) == 0 and len(non_json_files_remaining) == 1:
            print("✅ 清空输出目录功能测试通过")
            return True
        else:
            print("❌ 清空输出目录功能测试失败")
            return False

def test_clear_tables_logic():
    """测试清空表格逻辑"""
    print("\n=== 测试清空表格逻辑 ===")
    
    # 模拟表格列表
    tables = [
        {"name": "表格1", "description": "测试表格1", "goods": []},
        {"name": "表格2", "description": "测试表格2", "goods": []},
        {"name": "表格3", "description": "测试表格3", "goods": []}
    ]
    
    print(f"清空前表格数量: {len(tables)}")
    
    # 模拟清空操作
    tables.clear()
    
    print(f"清空后表格数量: {len(tables)}")
    
    if len(tables) == 0:
        print("✅ 清空表格逻辑测试通过")
        return True
    else:
        print("❌ 清空表格逻辑测试失败")
        return False

def main():
    """主测试函数"""
    print("开始测试清空功能...\n")
    
    test1_passed = test_clear_output_directory()
    test2_passed = test_clear_tables_logic()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"清空输出目录功能: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"清空表格功能: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有清空功能测试通过！")
    else:
        print("\n❌ 部分测试失败，需要检查代码。")

if __name__ == "__main__":
    main()
