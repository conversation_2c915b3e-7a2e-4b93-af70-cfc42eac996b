#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量转换功能使用示例
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from batch_converter import BatchConverter

def create_sample_config():
    """创建示例配置"""
    sample_config = {
        "default_config": {
            "entity.intoKey.id": "STOCKADVANCE_IN_PURCHASE",
            "entity.storeroom.id": "2c901acf942ba24f01942bb7166c1e61",
            "entity.operatorUser": "汕尾城区护理院",
            "entity.manuf.id": "2c901acf942ba24f01942bb69a9b1e5a",
            "entity.manufLinkTellp": "110",
            "entity.manufAddress1": "测试地址"
        },
        "tables": [
            {
                "name": "示例表格1",
                "description": "这是一个示例表格",
                "config": {
                    "entity.intoDate": "2025-01-20 10:00:00",
                    "entity.deliveryNnoteNo": "DN001",
                    "entity.notes": "示例入库"
                },
                "goods": [
                    {
                        "name": "瘦肉",
                        "quantity": "5",
                        "price": "20",
                        "unit": "g",
                        "batchNo": "B001",
                        "productionDate": "2025-01-18",
                        "expireDate": "2025-01-25",
                        "notes": "新鲜瘦肉"
                    },
                    {
                        "name": "莲蓉包",
                        "quantity": "10",
                        "price": "2",
                        "unit": "个",
                        "batchNo": "B002",
                        "productionDate": "2025-01-19",
                        "expireDate": "2025-01-22",
                        "notes": "早餐用"
                    }
                ]
            }
        ],
        "predefined_options": {
            "entity.intoKey.id": {
                "STOCKADVANCE_IN_PURCHASE": "采购入库",
                "STOCKADVANCE_IN_RETURN": "退货入库",
                "STOCKADVANCE_IN_TRANSFER": "调拨入库"
            },
            "entity.storeroom.id": {
                "2c901acf942ba24f01942bb7166c1e61": "主仓库",
                "2c901acf942ba24f01942bb7166c1e62": "副仓库"
            },
            "entity.operatorUser": {
                "汕尾城区护理院": "汕尾城区护理院",
                "管理员": "管理员"
            }
        }
    }
    
    with open('batch_config.json', 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print("示例配置已创建")

def main():
    """主函数"""
    # 创建主窗口
    root = tk.Tk()
    root.title("批量转换功能演示")
    root.geometry("500x400")
    
    # 加载物品数据
    item_data = {}
    if os.path.exists('item.json'):
        with open('item.json', 'r', encoding='utf-8') as f:
            item_data = json.load(f)
    else:
        messagebox.showwarning("警告", "未找到 item.json 文件，请确保文件存在")
    
    # 创建界面
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # 标题
    title_label = ttk.Label(main_frame, text="批量转换功能演示", font=('微软雅黑', 16, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # 说明文本
    info_text = """
    这个演示展示了批量转换功能的使用方法：
    
    1. 点击"创建示例配置"创建一个示例配置文件
    2. 点击"打开批量转换器"开始使用功能
    3. 在批量转换器中可以：
       - 配置默认设置
       - 添加/编辑表格
       - 配置商品信息
       - 执行批量转换
    
    转换后的JSON文件将保存在 out 文件夹中。
    """
    
    info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(pady=(0, 20))
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=10)
    
    def create_sample():
        try:
            create_sample_config()
            messagebox.showinfo("成功", "示例配置已创建！")
        except Exception as e:
            messagebox.showerror("错误", f"创建示例配置失败：{str(e)}")
    
    def open_batch_converter():
        try:
            batch_converter = BatchConverter(root, item_data)
        except Exception as e:
            messagebox.showerror("错误", f"打开批量转换器失败：{str(e)}")
    
    def show_output_folder():
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'out')
        if os.path.exists(output_dir):
            os.startfile(output_dir)
        else:
            messagebox.showinfo("信息", "输出文件夹尚未创建，请先执行批量转换")
    
    # 按钮
    ttk.Button(button_frame, text="创建示例配置", command=create_sample).pack(pady=5, fill=tk.X)
    ttk.Button(button_frame, text="打开批量转换器", command=open_batch_converter).pack(pady=5, fill=tk.X)
    ttk.Button(button_frame, text="查看输出文件夹", command=show_output_folder).pack(pady=5, fill=tk.X)
    
    # 状态信息
    status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding=10)
    status_frame.pack(fill=tk.X, pady=(20, 0))
    
    # 检查文件状态
    item_status = "✓ 存在" if os.path.exists('item.json') else "✗ 不存在"
    config_status = "✓ 存在" if os.path.exists('batch_config.json') else "✗ 不存在"
    output_status = "✓ 存在" if os.path.exists('out') else "✗ 不存在"
    
    ttk.Label(status_frame, text=f"item.json: {item_status}").pack(anchor=tk.W)
    ttk.Label(status_frame, text=f"batch_config.json: {config_status}").pack(anchor=tk.W)
    ttk.Label(status_frame, text=f"out 文件夹: {output_status}").pack(anchor=tk.W)
    
    # 运行
    root.mainloop()

if __name__ == "__main__":
    main()