#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量粘贴表格的物品匹配功能
"""

from item_matcher import ItemMatcher

def test_find_missing_items():
    """测试查找缺失物品功能"""
    print("=== 测试查找缺失物品功能 ===")
    
    # 模拟item.json中的物品数据
    available_items = {
        "大米": {"id": "001", "no": "DM001", "unit": "袋"},
        "面条": {"id": "002", "no": "MT002", "unit": "包"},
        "食用油": {"id": "003", "no": "SYY003", "unit": "瓶"},
        "鸡蛋": {"id": "004", "no": "JD004", "unit": "个"}
    }
    
    # 模拟表格中的物品列表（包含一些不存在的物品）
    table_items = [
        "大米",      # 存在
        "面条",      # 存在  
        "牛奶",      # 不存在
        "食用油",    # 存在
        "面包",      # 不存在
        "鸡蛋",      # 存在
        "苹果"       # 不存在
    ]
    
    print(f"可用物品: {list(available_items.keys())}")
    print(f"表格物品: {table_items}")
    
    # 查找缺失的物品
    missing_items = ItemMatcher.find_missing_items(table_items, available_items)
    
    print(f"缺失物品: {missing_items}")
    
    expected_missing = ["牛奶", "面包", "苹果"]
    
    if set(missing_items) == set(expected_missing):
        print("✅ 查找缺失物品功能测试通过")
        return True
    else:
        print(f"❌ 查找缺失物品功能测试失败，期望: {expected_missing}, 实际: {missing_items}")
        return False

def test_apply_replacements():
    """测试应用物品替换功能"""
    print("\n=== 测试应用物品替换功能 ===")
    
    # 模拟表格数据
    tables = [
        {
            'name': '测试表格1',
            'goods': [
                {'name': '牛奶', 'quantity': '10', 'price': '5.0', 'unit': '瓶'},
                {'name': '大米', 'quantity': '5', 'price': '20.0', 'unit': '袋'},
                {'name': '面包', 'quantity': '8', 'price': '3.0', 'unit': '个'}
            ]
        },
        {
            'name': '测试表格2', 
            'goods': [
                {'name': '苹果', 'quantity': '15', 'price': '8.0', 'unit': '斤'},
                {'name': '鸡蛋', 'quantity': '30', 'price': '1.0', 'unit': '个'}
            ]
        }
    ]
    
    # 模拟用户选择的替换映射
    replacements = {
        '牛奶': '豆奶',
        '面包': '馒头',
        '苹果': '香蕉'
    }
    
    # 模拟item_data
    item_data = {
        '豆奶': {'unit': '盒'},
        '馒头': {'unit': '个'},
        '香蕉': {'unit': '根'},
        '大米': {'unit': '袋'},
        '鸡蛋': {'unit': '个'}
    }
    
    print("替换前的表格:")
    for i, table in enumerate(tables):
        print(f"  表格{i+1}: {[goods['name'] for goods in table['goods']]}")
    
    print(f"替换映射: {replacements}")
    
    # 应用替换
    for table in tables:
        for goods in table.get('goods', []):
            original_name = goods.get('name', '')
            if original_name in replacements:
                replacement_name = replacements[original_name]
                goods['name'] = replacement_name
                print(f"替换物品: {original_name} -> {replacement_name}")
                
                # 更新单位信息
                if replacement_name in item_data:
                    goods['unit'] = item_data[replacement_name].get('unit', goods.get('unit', ''))
    
    print("替换后的表格:")
    for i, table in enumerate(tables):
        print(f"  表格{i+1}: {[goods['name'] for goods in table['goods']]}")
    
    # 验证替换结果
    expected_items = [
        ['豆奶', '大米', '馒头'],  # 表格1
        ['香蕉', '鸡蛋']          # 表格2
    ]
    
    actual_items = []
    for table in tables:
        actual_items.append([goods['name'] for goods in table['goods']])
    
    if actual_items == expected_items:
        print("✅ 应用物品替换功能测试通过")
        return True
    else:
        print(f"❌ 应用物品替换功能测试失败，期望: {expected_items}, 实际: {actual_items}")
        return False

def test_batch_paste_workflow():
    """测试批量粘贴表格的完整工作流程"""
    print("\n=== 测试批量粘贴表格工作流程 ===")
    
    # 模拟完整的工作流程
    print("1. 解析表格数据...")
    
    # 模拟解析出的表格
    parsed_tables = [
        {
            'name': '非城区特困长者 2025.06.10',
            'goods': [
                {'name': '大米', 'quantity': '10', 'price': '25.0'},
                {'name': '牛奶', 'quantity': '5', 'price': '8.0'},  # 不存在
                {'name': '食用油', 'quantity': '2', 'price': '15.0'}
            ]
        }
    ]
    
    # 模拟item_data
    item_data = {
        '大米': {'id': '001', 'unit': '袋'},
        '食用油': {'id': '003', 'unit': '瓶'},
        '豆奶': {'id': '005', 'unit': '盒'}  # 替代品
    }
    
    print("2. 检查缺失物品...")
    all_items = []
    for table in parsed_tables:
        for goods in table.get('goods', []):
            item_name = goods.get('name', '').strip()
            if item_name:
                all_items.append(item_name)
    
    missing_items = ItemMatcher.find_missing_items(all_items, item_data)
    print(f"   缺失物品: {missing_items}")
    
    if missing_items:
        print("3. 模拟用户选择替代物品...")
        replacements = {'牛奶': '豆奶'}  # 用户选择
        
        print("4. 应用替换...")
        for table in parsed_tables:
            for goods in table.get('goods', []):
                original_name = goods.get('name', '')
                if original_name in replacements:
                    replacement_name = replacements[original_name]
                    goods['name'] = replacement_name
                    if replacement_name in item_data:
                        goods['unit'] = item_data[replacement_name].get('unit', goods.get('unit', ''))
    
    print("5. 最终表格:")
    for table in parsed_tables:
        print(f"   {table['name']}: {[goods['name'] for goods in table['goods']]}")
    
    # 验证最终结果
    final_items = [goods['name'] for goods in parsed_tables[0]['goods']]
    expected_final = ['大米', '豆奶', '食用油']
    
    if final_items == expected_final:
        print("✅ 批量粘贴表格工作流程测试通过")
        return True
    else:
        print(f"❌ 批量粘贴表格工作流程测试失败，期望: {expected_final}, 实际: {final_items}")
        return False

def main():
    """主测试函数"""
    print("开始测试批量粘贴表格的物品匹配功能...\n")
    
    test1_passed = test_find_missing_items()
    test2_passed = test_apply_replacements()
    test3_passed = test_batch_paste_workflow()
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"查找缺失物品: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"应用物品替换: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"完整工作流程: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有物品匹配功能测试通过！")
    else:
        print("\n❌ 部分测试失败，需要检查代码。")

if __name__ == "__main__":
    main()
