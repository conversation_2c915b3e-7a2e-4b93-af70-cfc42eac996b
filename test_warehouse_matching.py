#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试仓库智能匹配功能
"""

def find_best_warehouse_match(warehouse_note, storeroom_options):
    """智能匹配最相似的仓库名称"""
    print(f"匹配仓库备注: {warehouse_note}")
    print(f"可选仓库: {storeroom_options}")
    
    # 计算每个仓库选项的匹配分数
    best_match = storeroom_options[0]  # 默认第一个
    best_score = 0

    for option in storeroom_options:
        score = 0
        print(f"  检查选项: {option}")

        # 完全匹配得最高分
        if warehouse_note == option.replace("仓库", ""):
            score += 1000
            print(f"    完全匹配: +1000, 总分: {score}")

        # 精确包含匹配
        if warehouse_note in option:
            score += 500
            print(f"    包含匹配: +500, 总分: {score}")

        # 特殊处理：城区 vs 非城区
        if warehouse_note == "城区特困长者":
            if "城区特困长者" in option and "非城区" not in option:
                score += 200
                print(f"    城区特困长者精确匹配: +200, 总分: {score}")
            elif "非城区" in option:
                score = 0  # 完全排除非城区选项
                print(f"    排除非城区选项: 分数清零")
                
        elif warehouse_note == "非城区特困长者":
            if "非城区特困长者" in option:
                score += 200
                print(f"    非城区特困长者精确匹配: +200, 总分: {score}")
            elif "城区特困长者" in option and "非城区" not in option:
                score = 0  # 完全排除城区选项
                print(f"    排除城区选项: 分数清零")

        # 其他关键词匹配
        if "员工" in warehouse_note and "员工" in option:
            score += 100
            print(f"    员工匹配: +100, 总分: {score}")

        if "自费" in warehouse_note and "自费" in option:
            score += 100
            print(f"    自费匹配: +100, 总分: {score}")

        print(f"    最终分数: {score}")
        
        if score > best_score:
            best_score = score
            best_match = option
            print(f"    新的最佳匹配: {best_match} (分数: {best_score})")

    print(f"最终选择: {best_match} (分数: {best_score})")
    return best_match

def test_warehouse_matching():
    """测试仓库匹配功能"""
    
    # 模拟仓库选项
    storeroom_options = [
        "非城区特困长者仓库",
        "员工仓库", 
        "城区特困长者仓库",
        "自费长者仓库"
    ]
    
    # 测试用例
    test_cases = [
        "非城区特困长者",
        "员工",
        "城区特困长者",
        "自费长者"
    ]
    
    print("=== 仓库智能匹配测试 ===\n")
    
    for warehouse_note in test_cases:
        print(f"{'='*50}")
        result = find_best_warehouse_match(warehouse_note, storeroom_options)
        expected = f"{warehouse_note}仓库"
        
        if result == expected:
            print(f"✅ 测试通过: {warehouse_note} -> {result}")
        else:
            print(f"❌ 测试失败: {warehouse_note} -> {result} (期望: {expected})")
        print()

if __name__ == "__main__":
    test_warehouse_matching()
